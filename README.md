# RFP Bidder Scoring System

An automated system for scoring bidder documents against RFP (Request for Proposal) criteria using open source Large Language Models (LLMs).

## Overview

This system processes multiple RFP documents and bidder proposals, extracts evaluation criteria, analyzes bidder qualifications, and automatically assigns scores based on the RFP requirements. It uses open source models like Llama and Sentence Transformers for natural language processing.

## Features

- **Multi-format Document Processing**: Supports PDF, DOCX, and TXT files
- **Automated Criteria Extraction**: Extracts evaluation criteria from RFP documents
- **Intelligent Bidder Analysis**: Analyzes bidder documents against specific criteria
- **Automated Scoring**: Assigns marks based on RFP marking systems
- **Batch Processing**: Handles multiple RFP and bidder documents simultaneously
- **Comprehensive Reporting**: Generates detailed scorecards and rankings
- **Configurable System**: Supports different RFP types and scoring methodologies
- **Open Source Models**: Uses Ollama (Llama) and HuggingFace models

## System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   RFP Documents │    │ Bidder Documents │    │  Configuration  │
└─────────┬───────┘    └─────────┬────────┘    └─────────┬───────┘
          │                      │                       │
          ▼                      ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                Document Processor                               │
│  • PDF/DOCX/TXT extraction  • Document classification          │
└─────────────────────┬───────────────────────────────────────────┘
                      │
          ┌───────────┴────────────┐
          ▼                        ▼
┌─────────────────┐    ┌─────────────────────┐
│ Criteria        │    │ Bidder Analyzer     │
│ Extractor       │    │ • RAG-based search  │
│ • LLM extraction│    │ • Information       │
│ • Table parsing │    │   extraction        │
└─────────┬───────┘    └─────────┬───────────┘
          │                      │
          └───────────┬──────────┘
                      ▼
          ┌─────────────────────┐
          │   Scoring Engine    │
          │ • Rule-based scoring│
          │ • LLM-based scoring │
          │ • Confidence scores │
          └─────────┬───────────┘
                    ▼
          ┌─────────────────────┐
          │   Report Generator  │
          │ • Scorecards        │
          │ • Rankings          │
          │ • Summary reports   │
          └─────────────────────┘
```

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd rfp-scoring-system
   ```

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Install Ollama** (for LLM support):
   ```bash
   # On Linux/Mac
   curl -fsSL https://ollama.ai/install.sh | sh
   
   # On Windows
   # Download from https://ollama.ai/download
   ```

4. **Pull the required model**:
   ```bash
   ollama pull llama3.2:3b
   ```

## Quick Start

1. **Place your documents** in the `documents/` folder:
   - RFP documents (e.g., `QCBS 1.pdf`, `RFP_Tender_Document 1.pdf`)
   - Bidder documents (e.g., `Dummy Bidder Document1.pdf`)

2. **Run the complete evaluation**:
   ```bash
   python rfp_scoring_system.py
   ```

3. **View results** in the `reports/` folder:
   - `evaluation_summary.json` - Overall summary
   - `bidder_rankings.csv` - Ranked bidder list
   - `detailed_scores.csv` - Detailed scoring breakdown

## Usage Examples

### Basic Usage
```python
from rfp_scoring_system import RFPScoringSystem

# Initialize system
system = RFPScoringSystem(
    documents_dir="documents",
    model_name="llama3.2:3b",
    minimum_passing_score=70.0
)

# Run complete evaluation
summary = system.run_complete_evaluation()
system.print_summary()
```

### Step-by-Step Processing
```python
# Process documents
processed_docs = system.process_documents()

# Extract criteria
criteria = system.extract_criteria()

# Analyze bidders
analysis_results = system.analyze_bidders()

# Score bidders
scorecards = system.score_bidders()

# Generate reports
reports = system.generate_reports()
```

### Custom Configuration
```python
from config import ConfigManager

config = ConfigManager()
config.set("scoring.minimum_passing_score", 75.0)
config.set("models.primary_llm.name", "llama3.2:1b")
config.update_scoring_weights({
    "experience": 1.5,
    "technical": 1.2,
    "financial": 1.0
})
```

## Configuration

The system uses a flexible configuration system (`config.py`) that supports:

- **Model Configuration**: LLM models, embedding models, parameters
- **Scoring Configuration**: Passing scores, weights, thresholds
- **Processing Configuration**: Chunk sizes, similarity search parameters
- **RFP Type Configuration**: Different RFP types with specific requirements

### Example Configuration
```json
{
  "models": {
    "primary_llm": {
      "name": "llama3.2:3b",
      "temperature": 0.1,
      "max_tokens": 2000
    }
  },
  "scoring": {
    "minimum_passing_score": 70.0,
    "confidence_threshold": 0.7,
    "scoring_weights": {
      "experience": 1.0,
      "financial": 1.0,
      "technical": 1.0
    }
  }
}
```

## File Structure

```
rfp-scoring-system/
├── documents/                  # Input documents
│   ├── QCBS 1.pdf             # RFP document
│   ├── Dummy Bidder Document1.pdf  # Bidder document
│   └── ...
├── reports/                   # Generated reports
├── document_processor.py     # Document processing
├── criteria_extractor.py     # Criteria extraction
├── bidder_analyzer.py        # Bidder analysis
├── scoring_engine.py         # Scoring logic
├── rfp_scoring_system.py     # Main application
├── config.py                 # Configuration management
├── test_system.py           # Test suite
├── requirements.txt         # Python dependencies
├── combined_table.txt       # Existing criteria (JSON)
└── README.md               # This file
```

## Testing

Run the test suite to validate the system:

```bash
python test_system.py
```

The test suite includes:
- Unit tests for all components
- Integration tests
- Validation with provided documents
- Configuration validation

## Supported Document Types

- **PDF**: Using `pdfplumber` for text and table extraction
- **DOCX**: Using `python-docx` for text extraction
- **TXT**: Direct text file reading

## Scoring Methodology

The system supports multiple scoring approaches:

1. **Rule-based Scoring**: Direct application of RFP marking systems
2. **LLM-based Scoring**: Intelligent evaluation using language models
3. **Hybrid Scoring**: Combination of rule-based and LLM scoring
4. **Confidence Scoring**: Reliability assessment of scoring decisions

## Output Formats

- **JSON**: Detailed results with full metadata
- **CSV**: Tabular data for spreadsheet analysis
- **Excel**: Formatted reports with multiple sheets
- **PDF**: Professional scorecards (planned)

## Troubleshooting

### Common Issues

1. **Ollama not running**:
   ```bash
   ollama serve
   ```

2. **Model not found**:
   ```bash
   ollama pull llama3.2:3b
   ```

3. **Memory issues with large documents**:
   - Reduce `chunk_size` in configuration
   - Use smaller model (e.g., `llama3.2:1b`)

4. **Poor scoring accuracy**:
   - Increase `confidence_threshold`
   - Enable manual review for low-confidence scores
   - Adjust scoring weights

### Logs

Check `rfp_scoring.log` for detailed system logs and error messages.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- **Ollama** for local LLM inference
- **LangChain** for LLM orchestration
- **HuggingFace** for embedding models
- **pdfplumber** for PDF processing
