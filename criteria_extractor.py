"""
Criteria Extraction System for RFP Documents

This module extracts evaluation criteria, scoring rubrics, and requirements from RFP documents
using open source LLMs and structured parsing techniques.
"""

import json
import re
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import pdfplumber
from langchain_ollama import ChatOllama
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser
from document_processor import ProcessedDocument, EvaluationCriteria

logger = logging.getLogger(__name__)

class CriteriaExtractor:
    """Extract evaluation criteria from RFP documents"""
    
    def __init__(self, model_name: str = "llama3.2:3b"):
        self.llm = ChatOllama(model=model_name)
        self.setup_prompts()
    
    def setup_prompts(self):
        """Setup prompt templates for criteria extraction"""
        
        self.criteria_extraction_prompt = PromptTemplate.from_template("""
You are an expert at analyzing RFP (Request for Proposal) documents and extracting evaluation criteria.

Analyze the following RFP document content and extract ALL evaluation criteria with their scoring information.

Document Content:
{document_content}

Please extract and structure the information in the following JSON format:
{{
  "evaluation_criteria": [
    {{
      "serial_number": "1",
      "criteria_text": "The specific requirement or criteria",
      "relevant_document": "Required documents or evidence",
      "marking_system": "How marks are awarded (e.g., '10-15 yrs = 10, >15 yrs = 20')",
      "max_marks": "Maximum marks possible"
    }}
  ],
  "total_marks": "Total maximum marks",
  "minimum_passing_score": "Minimum score required to pass (if mentioned)",
  "additional_notes": "Any additional important information"
}}

Focus on:
1. Technical evaluation criteria
2. Experience requirements
3. Financial criteria
4. Team composition requirements
5. Methodology evaluation
6. Any other scoring criteria

Be precise and extract exact text from the document. If marking system is not clear, note it as "Not specified".
""")

        self.table_parsing_prompt = PromptTemplate.from_template("""
You are analyzing a table from an RFP document that contains evaluation criteria.

Table Data:
{table_data}

Convert this table data into structured evaluation criteria. Extract:
1. Serial numbers or criteria IDs
2. Evaluation criteria descriptions
3. Required documents
4. Marking/scoring systems
5. Maximum marks

Return the result in JSON format following this structure:
{{
  "criteria": [
    {{
      "serial_number": "",
      "criteria_text": "",
      "relevant_document": "",
      "marking_system": "",
      "max_marks": ""
    }}
  ]
}}

If a row doesn't contain criteria (like headers or totals), skip it or mark appropriately.
""")

    def extract_tables_from_pdf(self, file_path: str) -> List[Dict[str, Any]]:
        """Extract and structure tables from PDF"""
        try:
            all_tables_data = []
            with pdfplumber.open(file_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    tables = page.extract_tables()
                    for table_num, table in enumerate(tables):
                        if table and len(table) > 1:
                            # Convert table to structured format
                            headers = table[0] if table[0] else []
                            rows = table[1:] if len(table) > 1 else []
                            
                            table_data = {
                                'page': page_num + 1,
                                'table_number': table_num + 1,
                                'headers': headers,
                                'rows': rows,
                                'raw_table': table
                            }
                            all_tables_data.append(table_data)
            
            return all_tables_data
        except Exception as e:
            logger.error(f"Error extracting tables: {str(e)}")
            return []

    def parse_table_with_llm(self, table_data: Dict[str, Any]) -> List[EvaluationCriteria]:
        """Use LLM to parse table data into structured criteria"""
        try:
            # Format table data for LLM
            formatted_table = f"Headers: {table_data['headers']}\n\nRows:\n"
            for i, row in enumerate(table_data['rows']):
                formatted_table += f"Row {i+1}: {row}\n"
            
            # Get LLM response
            chain = self.table_parsing_prompt | self.llm | StrOutputParser()
            response = chain.invoke({"table_data": formatted_table})
            
            # Parse JSON response
            try:
                parsed_data = json.loads(response)
                criteria_list = []
                
                for criteria_data in parsed_data.get('criteria', []):
                    if criteria_data.get('criteria_text', '').strip():
                        criteria = EvaluationCriteria(
                            serial_number=criteria_data.get('serial_number', ''),
                            criteria_text=criteria_data.get('criteria_text', ''),
                            relevant_document=criteria_data.get('relevant_document', ''),
                            marking_system=criteria_data.get('marking_system', ''),
                            max_marks=criteria_data.get('max_marks', '')
                        )
                        criteria_list.append(criteria)
                
                return criteria_list
            except json.JSONDecodeError:
                logger.error("Failed to parse LLM response as JSON")
                return []
                
        except Exception as e:
            logger.error(f"Error parsing table with LLM: {str(e)}")
            return []

    def extract_criteria_from_text(self, document_content: str) -> Dict[str, Any]:
        """Extract criteria from document text using LLM"""
        try:
            chain = self.criteria_extraction_prompt | self.llm | StrOutputParser()
            response = chain.invoke({"document_content": document_content})
            
            # Parse JSON response
            try:
                return json.loads(response)
            except json.JSONDecodeError:
                logger.error("Failed to parse criteria extraction response as JSON")
                return {}
                
        except Exception as e:
            logger.error(f"Error extracting criteria from text: {str(e)}")
            return {}

    def extract_from_processed_document(self, processed_doc: ProcessedDocument) -> Dict[str, Any]:
        """Extract criteria from a processed RFP document"""
        if processed_doc.document_type != 'rfp':
            logger.warning(f"Document {processed_doc.filename} is not an RFP document")
            return {}
        
        logger.info(f"Extracting criteria from: {processed_doc.filename}")
        
        # Method 1: Extract from tables (if PDF)
        table_criteria = []
        if processed_doc.file_path.endswith('.pdf'):
            tables = self.extract_tables_from_pdf(processed_doc.file_path)
            for table in tables:
                criteria = self.parse_table_with_llm(table)
                table_criteria.extend(criteria)
        
        # Method 2: Extract from full text
        text_criteria = self.extract_criteria_from_text(processed_doc.content)
        
        # Combine results
        result = {
            'document_name': processed_doc.filename,
            'extraction_methods': {
                'table_extraction': len(table_criteria) > 0,
                'text_extraction': len(text_criteria) > 0
            },
            'table_criteria': [criteria.to_dict() for criteria in table_criteria],
            'text_criteria': text_criteria,
            'metadata': processed_doc.metadata
        }
        
        return result

    def process_multiple_rfp_documents(self, rfp_documents: List[ProcessedDocument]) -> Dict[str, Any]:
        """Process multiple RFP documents and extract all criteria"""
        all_criteria = {}
        
        for doc in rfp_documents:
            criteria = self.extract_from_processed_document(doc)
            if criteria:
                all_criteria[doc.filename] = criteria
        
        return all_criteria

    def save_extracted_criteria(self, criteria_data: Dict[str, Any], 
                               output_file: str = "extracted_criteria.json"):
        """Save extracted criteria to JSON file"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(criteria_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Extracted criteria saved to: {output_file}")

    def load_existing_criteria(self, criteria_file: str = "combined_table.txt") -> List[EvaluationCriteria]:
        """Load existing criteria from combined_table.txt"""
        try:
            with open(criteria_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            criteria_list = []
            for item in data:
                if isinstance(item, dict) and item.get('Technical Evaluation Criteria', '').strip():
                    criteria = EvaluationCriteria(
                        serial_number=item.get('S.N.', ''),
                        criteria_text=item.get('Technical Evaluation Criteria', ''),
                        relevant_document=item.get('Relevant Document', ''),
                        marking_system=item.get('Marking System', ''),
                        max_marks=item.get('Max.\nMarks', '')
                    )
                    criteria_list.append(criteria)
            
            return criteria_list
        except Exception as e:
            logger.error(f"Error loading existing criteria: {str(e)}")
            return []

if __name__ == "__main__":
    # Example usage
    from document_processor import DocumentProcessor
    
    # Process documents first
    processor = DocumentProcessor()
    processed_docs = processor.process_all_documents()
    
    # Extract criteria from RFP documents
    extractor = CriteriaExtractor()
    criteria_data = extractor.process_multiple_rfp_documents(processed_docs['rfp'])
    extractor.save_extracted_criteria(criteria_data)
    
    # Also load existing criteria
    existing_criteria = extractor.load_existing_criteria()
    print(f"Loaded {len(existing_criteria)} existing criteria")
    
    print(f"Extracted criteria from {len(criteria_data)} RFP documents")
    for doc_name, criteria in criteria_data.items():
        table_count = len(criteria.get('table_criteria', []))
        text_count = len(criteria.get('text_criteria', {}).get('evaluation_criteria', []))
        print(f"  {doc_name}: {table_count} table criteria, {text_count} text criteria")
