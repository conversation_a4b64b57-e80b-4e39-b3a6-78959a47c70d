import pdfplumber
import json
all_rows = []
header = None

with pdfplumber.open("./documents/QCBS 1.pdf") as pdf:
    for page in pdf.pages:
        tables = page.extract_tables()
        for table in tables:
            if not table or len(table) < 2:
                continue  # skip empty or malformed tables
            # Use the first row as header if not set
            if header is None:
                header = table[0]
                all_rows.extend(table[1:])
            else:
                # Check if this table's header matches the previous header
                if table[0] == header:
                    all_rows.extend(table[1:])
                else:
                    all_rows.extend(table)  # Sometimes header is missing, so add all

# Convert to list of dicts for better JSON structure
if header:
    data = [dict(zip(header, row)) for row in all_rows if len(row) == len(header)]
else:
    data = all_rows  # fallback to raw rows

with open("combined_table.txt", "w", encoding="utf-8") as f:
    json.dump(data, f, ensure_ascii=False, indent=2)
