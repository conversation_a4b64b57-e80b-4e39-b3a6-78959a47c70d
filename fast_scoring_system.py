#!/usr/bin/env python3
"""
Fast RFP Scoring System - Optimized for Speed

This version minimizes LLM calls by:
1. <PERSON><PERSON> processing all criteria in a single call
2. Rule-based scoring for common patterns
3. Simplified text processing
4. Parallel processing where possible
"""

import os
import json
import logging
import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, as_completed

from document_processor import DocumentProcessor
from criteria_extractor import CriteriaExtractor
from langchain_ollama import ChatOllama
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class BatchScore:
    """Batch scoring result for all criteria"""
    bidder_name: str
    scores: Dict[str, float]  # criteria_id -> score
    rationales: Dict[str, str]  # criteria_id -> rationale
    total_score: float
    max_possible: float
    percentage: float

class FastScoringSystem:
    """High-performance RFP scoring system with minimal LLM calls"""
    
    def __init__(self, model_name: str = "llama3.2:3b"):
        self.model_name = model_name
        self.llm = ChatOllama(model=model_name)
        self.document_processor = DocumentProcessor()
        self.criteria_extractor = CriteriaExtractor(model_name)
        
        # Single batch scoring prompt for all criteria
        self.batch_scoring_prompt = PromptTemplate.from_template("""
        You are evaluating a bidder against multiple RFP criteria. Analyze the bidder information and provide scores for each criteria.

        BIDDER INFORMATION:
        {bidder_info}

        EVALUATION CRITERIA:
        {all_criteria}

        For each criteria, provide:
        - Score (0 to max marks)
        - Brief justification

        Format your response as:
        CRITERIA 1: [score]/[max] - [justification]
        CRITERIA 2: [score]/[max] - [justification]
        CRITERIA 3: [score]/[max] - [justification]
        etc.

        Be concise but accurate in your scoring.
        """)
    
    def parse_marking_rules(self, marking_system: str) -> Dict[str, Any]:
        """Fast rule-based parsing of marking systems"""
        if not marking_system:
            return {}
        
        rules = {}
        
        # Common patterns with regex
        patterns = {
            'years_range': r'(\d+)-(\d+)\s*(?:yrs?|years?)\s*=\s*(\d+)',
            'years_greater': r'>(\d+)\s*(?:yrs?|years?)\s*=\s*(\d+)',
            'amount_range': r'Rs\.?\s*(\d+)-(\d+)\s*(?:lac|lakh)\s*=\s*(\d+)',
            'amount_greater': r'>Rs\.?\s*(\d+)\s*(?:lac|lakh)\s*=\s*(\d+)',
            'simple_range': r'(\d+)-(\d+)\s*=\s*(\d+)',
            'simple_greater': r'>(\d+)\s*=\s*(\d+)'
        }
        
        for pattern_type, pattern in patterns.items():
            matches = re.findall(pattern, marking_system, re.IGNORECASE)
            if matches:
                rules[pattern_type] = matches
        
        return rules
    
    def apply_rule_scoring(self, bidder_info: str, criteria, rules: Dict[str, Any]) -> Optional[tuple[float, str]]:
        """Apply rule-based scoring if patterns match"""
        if not rules:
            return None
        
        # Extract numbers from bidder info
        numbers = [float(x) for x in re.findall(r'\d+(?:\.\d+)?', bidder_info)]
        if not numbers:
            return None
        
        max_marks = float(criteria.max_marks) if criteria.max_marks.isdigit() else 0.0
        
        # Check years patterns
        if 'years_range' in rules or 'years_greater' in rules:
            for num in numbers:
                # Check range rules
                for rule in rules.get('years_range', []):
                    min_val, max_val, marks = map(float, rule)
                    if min_val <= num <= max_val:
                        return min(float(marks), max_marks), f"Rule: {num} years in range {min_val}-{max_val}"
                
                # Check greater than rules
                for rule in rules.get('years_greater', []):
                    min_val, marks = map(float, rule)
                    if num > min_val:
                        return min(float(marks), max_marks), f"Rule: {num} years > {min_val}"
        
        # Check amount patterns
        if 'amount_range' in rules or 'amount_greater' in rules:
            for num in numbers:
                # Check amount range rules
                for rule in rules.get('amount_range', []):
                    min_val, max_val, marks = map(float, rule)
                    if min_val <= num <= max_val:
                        return min(float(marks), max_marks), f"Rule: Rs.{num} lac in range {min_val}-{max_val}"
                
                # Check amount greater rules
                for rule in rules.get('amount_greater', []):
                    min_val, marks = map(float, rule)
                    if num > min_val:
                        return min(float(marks), max_marks), f"Rule: Rs.{num} lac > {min_val}"
        
        return None
    
    def score_bidder_fast(self, bidder_doc, criteria_list: List) -> BatchScore:
        """Score bidder against all criteria in minimal LLM calls"""
        bidder_name = bidder_doc.filename.replace('.pdf', '').replace('Document', '').strip()
        
        # Step 1: Try rule-based scoring for all criteria
        scores = {}
        rationales = {}
        llm_needed_criteria = []
        
        logger.info(f"Applying rule-based scoring for {bidder_name}...")
        
        for criteria in criteria_list:
            if not criteria.criteria_text.strip():
                continue
                
            rules = self.parse_marking_rules(criteria.marking_system)
            rule_result = self.apply_rule_scoring(bidder_doc.content, criteria, rules)
            
            if rule_result:
                score, rationale = rule_result
                scores[criteria.serial_number] = score
                rationales[criteria.serial_number] = rationale
                logger.info(f"✅ Rule-based: Criteria {criteria.serial_number} = {score}")
            else:
                llm_needed_criteria.append(criteria)
        
        # Step 2: Single LLM call for remaining criteria
        if llm_needed_criteria:
            logger.info(f"Using LLM for {len(llm_needed_criteria)} criteria...")
            
            # Format all criteria for batch processing
            criteria_text = ""
            for i, criteria in enumerate(llm_needed_criteria, 1):
                criteria_text += f"\nCRITERIA {criteria.serial_number}: {criteria.criteria_text}\n"
                criteria_text += f"Max Marks: {criteria.max_marks}\n"
                criteria_text += f"Marking System: {criteria.marking_system}\n"
            
            try:
                # Single LLM call for all remaining criteria
                response = self.batch_scoring_prompt.format(
                    bidder_info=bidder_doc.content[:2000],  # Limit content length
                    all_criteria=criteria_text
                )
                
                chain = self.batch_scoring_prompt | self.llm | StrOutputParser()
                llm_response = chain.invoke({
                    "bidder_info": bidder_doc.content[:2000],
                    "all_criteria": criteria_text
                })
                
                # Parse batch response
                self.parse_batch_response(llm_response, llm_needed_criteria, scores, rationales)
                
            except Exception as e:
                logger.warning(f"LLM batch scoring failed: {str(e)}")
                # Fallback: assign zero scores
                for criteria in llm_needed_criteria:
                    scores[criteria.serial_number] = 0.0
                    rationales[criteria.serial_number] = "LLM scoring failed - manual review required"
        
        # Step 3: Calculate totals
        total_score = sum(scores.values())
        max_possible = sum(float(c.max_marks) if c.max_marks.isdigit() else 0.0 for c in criteria_list if c.criteria_text.strip())
        percentage = (total_score / max_possible * 100) if max_possible > 0 else 0
        
        return BatchScore(
            bidder_name=bidder_name,
            scores=scores,
            rationales=rationales,
            total_score=total_score,
            max_possible=max_possible,
            percentage=percentage
        )
    
    def parse_batch_response(self, response: str, criteria_list: List, scores: Dict, rationales: Dict):
        """Parse batch LLM response to extract scores"""
        lines = response.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or 'CRITERIA' not in line.upper():
                continue
            
            # Pattern: CRITERIA 1: 10/20 - justification
            pattern = r'CRITERIA\s+(\w+):\s*(\d+(?:\.\d+)?)/\d+\s*-\s*(.+)'
            match = re.search(pattern, line, re.IGNORECASE)
            
            if match:
                criteria_id, score, rationale = match.groups()
                try:
                    scores[criteria_id] = float(score)
                    rationales[criteria_id] = rationale.strip()
                except ValueError:
                    continue
        
        # Fill in missing scores with fallback
        for criteria in criteria_list:
            if criteria.serial_number not in scores:
                scores[criteria.serial_number] = 0.0
                rationales[criteria.serial_number] = "Could not extract score from LLM response"
    
    def run_fast_evaluation(self) -> Dict[str, Any]:
        """Run fast evaluation with minimal LLM calls"""
        start_time = datetime.now()
        logger.info("🚀 Starting FAST RFP evaluation...")
        
        try:
            # Step 1: Process documents (no LLM calls)
            logger.info("📄 Processing documents...")
            processed_docs = self.document_processor.process_all_documents()
            
            # Step 2: Load criteria (no LLM calls)
            logger.info("📋 Loading criteria...")
            criteria_list = self.criteria_extractor.load_existing_criteria()
            
            if not criteria_list:
                return {"error": "No evaluation criteria found"}
            
            # Step 3: Score bidders (minimal LLM calls)
            results = {}
            total_llm_calls = 0
            
            for bidder_doc in processed_docs['bidder']:
                logger.info(f"⚡ Fast scoring: {bidder_doc.filename}")
                
                batch_score = self.score_bidder_fast(bidder_doc, criteria_list)
                
                # Count LLM calls (only for criteria that couldn't be rule-based)
                rule_based_count = sum(1 for r in batch_score.rationales.values() if r.startswith("Rule:"))
                llm_calls_for_bidder = 1 if len(batch_score.rationales) > rule_based_count else 0
                total_llm_calls += llm_calls_for_bidder
                
                results[batch_score.bidder_name] = {
                    "individual_scores": [
                        {
                            "criteria_id": cid,
                            "awarded_marks": score,
                            "max_marks": float(next(c.max_marks for c in criteria_list if c.serial_number == cid and c.max_marks.isdigit())),
                            "rationale": batch_score.rationales.get(cid, ""),
                            "scoring_method": "rule_based" if batch_score.rationales.get(cid, "").startswith("Rule:") else "llm_based"
                        }
                        for cid, score in batch_score.scores.items()
                    ],
                    "total_score": batch_score.total_score,
                    "max_possible_score": batch_score.max_possible,
                    "percentage_score": batch_score.percentage,
                    "qualification_status": "QUALIFIED" if batch_score.percentage >= 70 else "NOT_QUALIFIED"
                }
            
            # Performance metrics
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            summary = {
                "results": results,
                "performance_metrics": {
                    "total_duration_seconds": duration,
                    "total_llm_calls": total_llm_calls,
                    "average_time_per_bidder": duration / len(results) if results else 0,
                    "rule_based_scoring_percentage": 0  # Will calculate below
                },
                "evaluation_timestamp": end_time.isoformat()
            }
            
            # Calculate rule-based percentage
            total_scores = sum(len(r["individual_scores"]) for r in results.values())
            rule_based_scores = sum(
                sum(1 for s in r["individual_scores"] if s["scoring_method"] == "rule_based")
                for r in results.values()
            )
            summary["performance_metrics"]["rule_based_scoring_percentage"] = (
                rule_based_scores / total_scores * 100 if total_scores > 0 else 0
            )
            
            # Save results
            with open("fast_scoring_results.json", 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ Fast evaluation completed in {duration:.1f} seconds!")
            logger.info(f"📊 Total LLM calls: {total_llm_calls} (vs ~30+ in original)")
            logger.info(f"⚡ Rule-based scoring: {rule_based_scores}/{total_scores} ({rule_based_scores/total_scores*100:.1f}%)")
            
            return summary
            
        except Exception as e:
            logger.error(f"Fast evaluation failed: {str(e)}")
            return {"error": str(e)}
    
    def print_fast_results(self, results: Dict[str, Any]):
        """Print fast evaluation results with performance metrics"""
        if "error" in results:
            print(f"❌ Evaluation failed: {results['error']}")
            return
        
        metrics = results["performance_metrics"]
        
        print("\n" + "="*60)
        print("⚡ FAST RFP SCORING RESULTS")
        print("="*60)
        print(f"⏱️  Duration: {metrics['total_duration_seconds']:.1f} seconds")
        print(f"🤖 LLM Calls: {metrics['total_llm_calls']}")
        print(f"📏 Rule-based: {metrics['rule_based_scoring_percentage']:.1f}%")
        print(f"⚡ Speed: {metrics['average_time_per_bidder']:.1f}s per bidder")
        
        for bidder_name, bidder_data in results["results"].items():
            print(f"\n🏢 {bidder_name}")
            print(f"   Total Score: {bidder_data['total_score']:.1f}/{bidder_data['max_possible_score']:.1f}")
            print(f"   Percentage: {bidder_data['percentage_score']:.1f}%")
            print(f"   Status: {bidder_data['qualification_status']}")
            
            rule_count = sum(1 for s in bidder_data['individual_scores'] if s['scoring_method'] == 'rule_based')
            llm_count = len(bidder_data['individual_scores']) - rule_count
            print(f"   Scoring: {rule_count} rule-based, {llm_count} LLM-based")

def main():
    """Main function"""
    print("⚡ FAST RFP SCORING SYSTEM")
    print("Optimized for speed with minimal LLM calls")
    
    system = FastScoringSystem()
    results = system.run_fast_evaluation()
    system.print_fast_results(results)

if __name__ == "__main__":
    main()
