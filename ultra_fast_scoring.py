#!/usr/bin/env python3
"""
Ultra-Fast RFP Scoring System - Rule-Based Only

This version demonstrates maximum speed by using only rule-based scoring
and minimal LLM calls, showing the performance difference.
"""

import os
import json
import logging
import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import pandas as pd

from document_processor import DocumentProcessor
from criteria_extractor import CriteriaExtractor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class UltraFastScore:
    """Ultra-fast scoring result"""
    bidder_name: str
    criteria_scores: Dict[str, Dict[str, Any]]
    total_score: float
    max_possible: float
    percentage: float
    processing_time: float

class UltraFastScoringSystem:
    """Ultra-fast RFP scoring with rule-based processing only"""
    
    def __init__(self):
        self.document_processor = DocumentProcessor()
        self.criteria_extractor = CriteriaExtractor()
        
        # Pre-compiled regex patterns for speed
        self.patterns = {
            'years': re.compile(r'(\d+(?:\.\d+)?)\s*(?:years?|yrs?)', re.IGNORECASE),
            'amounts': re.compile(r'(?:Rs\.?\s*)?(\d+(?:\.\d+)?)\s*(?:lac|lakh|crore)', re.IGNORECASE),
            'projects': re.compile(r'(\d+(?:\.\d+)?)\s*(?:projects?|audits?|assignments?)', re.IGNORECASE),
            'numbers': re.compile(r'(\d+(?:\.\d+)?)', re.IGNORECASE)
        }
        
        # Rule definitions for common RFP patterns
        self.scoring_rules = {
            'registration_years': {
                'pattern': r'(\d+)-(\d+)\s*(?:yrs?|years?)\s*=\s*(\d+)',
                'greater_pattern': r'>(\d+)\s*(?:yrs?|years?)\s*=\s*(\d+)',
                'extract_from': 'years'
            },
            'financial_turnover': {
                'pattern': r'Rs\.?\s*(\d+)-(\d+)\s*(?:lac|lakh)\s*=\s*(\d+)',
                'greater_pattern': r'>Rs\.?\s*(\d+)\s*(?:lac|lakh)\s*=\s*(\d+)',
                'extract_from': 'amounts'
            },
            'project_count': {
                'pattern': r'(\d+)-(\d+)\s*=\s*(\d+)',
                'greater_pattern': r'>(\d+)\s*=\s*(\d+)',
                'extract_from': 'projects'
            }
        }
    
    def extract_values_fast(self, text: str, value_type: str) -> List[float]:
        """Fast value extraction using pre-compiled regex"""
        if value_type in self.patterns:
            matches = self.patterns[value_type].findall(text)
            return [float(match) for match in matches if match.replace('.', '').isdigit()]
        return []
    
    def apply_ultra_fast_scoring(self, bidder_content: str, criteria) -> Dict[str, Any]:
        """Ultra-fast rule-based scoring"""
        start_time = datetime.now()
        
        if not criteria.marking_system or not criteria.marking_system.strip():
            return {
                'score': 0.0,
                'max_marks': float(criteria.max_marks) if criteria.max_marks.isdigit() else 0.0,
                'method': 'no_marking_system',
                'rationale': 'No marking system defined',
                'processing_time_ms': (datetime.now() - start_time).total_seconds() * 1000
            }
        
        max_marks = float(criteria.max_marks) if criteria.max_marks.isdigit() else 0.0
        
        # Determine rule type based on criteria content
        rule_type = None
        if any(word in criteria.criteria_text.lower() for word in ['register', 'firm', 'years', 'experience']):
            rule_type = 'registration_years'
        elif any(word in criteria.criteria_text.lower() for word in ['turnover', 'financial', 'rs', 'lac', 'lakh']):
            rule_type = 'financial_turnover'
        elif any(word in criteria.criteria_text.lower() for word in ['audit', 'project', 'assignment']):
            rule_type = 'project_count'
        
        if not rule_type or rule_type not in self.scoring_rules:
            return {
                'score': 0.0,
                'max_marks': max_marks,
                'method': 'unsupported_criteria',
                'rationale': 'Criteria type not supported by rule-based scoring',
                'processing_time_ms': (datetime.now() - start_time).total_seconds() * 1000
            }
        
        rule_config = self.scoring_rules[rule_type]
        
        # Extract values from bidder content
        values = self.extract_values_fast(bidder_content, rule_config['extract_from'])
        if not values:
            return {
                'score': 0.0,
                'max_marks': max_marks,
                'method': 'no_values_found',
                'rationale': f'No {rule_config["extract_from"]} values found in bidder document',
                'processing_time_ms': (datetime.now() - start_time).total_seconds() * 1000
            }
        
        # Parse marking system rules
        marking_system = criteria.marking_system
        
        # Check range patterns
        range_matches = re.findall(rule_config['pattern'], marking_system, re.IGNORECASE)
        greater_matches = re.findall(rule_config['greater_pattern'], marking_system, re.IGNORECASE)
        
        # Apply scoring rules
        best_score = 0.0
        best_rationale = "No matching rule found"
        
        for value in values:
            # Check range rules
            for match in range_matches:
                min_val, max_val, marks = map(float, match)
                if min_val <= value <= max_val:
                    score = min(marks, max_marks)
                    if score > best_score:
                        best_score = score
                        best_rationale = f"Value {value} in range {min_val}-{max_val} → {score} marks"
            
            # Check greater than rules
            for match in greater_matches:
                min_val, marks = map(float, match)
                if value > min_val:
                    score = min(marks, max_marks)
                    if score > best_score:
                        best_score = score
                        best_rationale = f"Value {value} > {min_val} → {score} marks"
        
        return {
            'score': best_score,
            'max_marks': max_marks,
            'method': 'rule_based',
            'rationale': best_rationale,
            'values_found': values,
            'processing_time_ms': (datetime.now() - start_time).total_seconds() * 1000
        }
    
    def score_bidder_ultra_fast(self, bidder_doc, criteria_list: List) -> UltraFastScore:
        """Ultra-fast bidder scoring"""
        start_time = datetime.now()
        bidder_name = bidder_doc.filename.replace('.pdf', '').replace('Document', '').strip()
        
        logger.info(f"⚡ Ultra-fast scoring: {bidder_name}")
        
        criteria_scores = {}
        total_score = 0.0
        max_possible = 0.0
        
        for criteria in criteria_list:
            if not criteria.criteria_text.strip():
                continue
            
            score_result = self.apply_ultra_fast_scoring(bidder_doc.content, criteria)
            criteria_scores[criteria.serial_number] = score_result
            
            total_score += score_result['score']
            max_possible += score_result['max_marks']
            
            logger.info(f"  Criteria {criteria.serial_number}: {score_result['score']}/{score_result['max_marks']} ({score_result['method']})")
        
        processing_time = (datetime.now() - start_time).total_seconds()
        percentage = (total_score / max_possible * 100) if max_possible > 0 else 0
        
        return UltraFastScore(
            bidder_name=bidder_name,
            criteria_scores=criteria_scores,
            total_score=total_score,
            max_possible=max_possible,
            percentage=percentage,
            processing_time=processing_time
        )
    
    def run_ultra_fast_evaluation(self) -> Dict[str, Any]:
        """Run ultra-fast evaluation with zero LLM calls"""
        start_time = datetime.now()
        logger.info("🚀 Starting ULTRA-FAST RFP evaluation (Rule-based only)...")
        
        try:
            # Step 1: Process documents (no LLM calls)
            logger.info("📄 Processing documents...")
            doc_start = datetime.now()
            processed_docs = self.document_processor.process_all_documents()
            doc_time = (datetime.now() - doc_start).total_seconds()
            
            # Step 2: Load criteria (no LLM calls)
            logger.info("📋 Loading criteria...")
            criteria_start = datetime.now()
            criteria_list = self.criteria_extractor.load_existing_criteria()
            criteria_time = (datetime.now() - criteria_start).total_seconds()
            
            if not criteria_list:
                return {"error": "No evaluation criteria found"}
            
            # Step 3: Ultra-fast scoring (no LLM calls)
            logger.info("⚡ Ultra-fast rule-based scoring...")
            scoring_start = datetime.now()
            
            results = {}
            for bidder_doc in processed_docs['bidder']:
                ultra_score = self.score_bidder_ultra_fast(bidder_doc, criteria_list)
                
                results[ultra_score.bidder_name] = {
                    "individual_scores": [
                        {
                            "criteria_id": cid,
                            "awarded_marks": score_data['score'],
                            "max_marks": score_data['max_marks'],
                            "rationale": score_data['rationale'],
                            "scoring_method": score_data['method'],
                            "processing_time_ms": score_data['processing_time_ms'],
                            "values_found": score_data.get('values_found', [])
                        }
                        for cid, score_data in ultra_score.criteria_scores.items()
                    ],
                    "total_score": ultra_score.total_score,
                    "max_possible_score": ultra_score.max_possible,
                    "percentage_score": ultra_score.percentage,
                    "qualification_status": "QUALIFIED" if ultra_score.percentage >= 70 else "NOT_QUALIFIED",
                    "processing_time_seconds": ultra_score.processing_time
                }
            
            scoring_time = (datetime.now() - scoring_start).total_seconds()
            total_time = (datetime.now() - start_time).total_seconds()
            
            # Performance metrics
            total_criteria_processed = sum(len(r["individual_scores"]) for r in results.values())
            rule_based_count = sum(
                sum(1 for s in r["individual_scores"] if s["scoring_method"] == "rule_based")
                for r in results.values()
            )
            
            summary = {
                "results": results,
                "performance_metrics": {
                    "total_duration_seconds": total_time,
                    "document_processing_seconds": doc_time,
                    "criteria_loading_seconds": criteria_time,
                    "scoring_seconds": scoring_time,
                    "total_llm_calls": 0,  # Zero LLM calls!
                    "total_criteria_processed": total_criteria_processed,
                    "rule_based_scoring_count": rule_based_count,
                    "rule_based_percentage": (rule_based_count / total_criteria_processed * 100) if total_criteria_processed > 0 else 0,
                    "average_time_per_bidder": total_time / len(results) if results else 0,
                    "average_time_per_criteria_ms": (scoring_time * 1000) / total_criteria_processed if total_criteria_processed > 0 else 0
                },
                "evaluation_timestamp": datetime.now().isoformat()
            }
            
            # Save results
            with open("ultra_fast_results.json", 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ Ultra-fast evaluation completed!")
            logger.info(f"⏱️  Total time: {total_time:.2f} seconds")
            logger.info(f"🤖 LLM calls: 0 (vs 30+ in original)")
            logger.info(f"📏 Rule-based scoring: {rule_based_count}/{total_criteria_processed} ({rule_based_count/total_criteria_processed*100:.1f}%)")
            logger.info(f"⚡ Speed: {total_time/len(results):.2f}s per bidder")
            
            return summary
            
        except Exception as e:
            logger.error(f"Ultra-fast evaluation failed: {str(e)}")
            return {"error": str(e)}
    
    def print_ultra_fast_results(self, results: Dict[str, Any]):
        """Print ultra-fast results with detailed performance metrics"""
        if "error" in results:
            print(f"❌ Evaluation failed: {results['error']}")
            return
        
        metrics = results["performance_metrics"]
        
        print("\n" + "="*70)
        print("⚡ ULTRA-FAST RFP SCORING RESULTS (Rule-Based Only)")
        print("="*70)
        print(f"⏱️  Total Duration: {metrics['total_duration_seconds']:.2f} seconds")
        print(f"🤖 LLM Calls: {metrics['total_llm_calls']} (ZERO!)")
        print(f"📏 Rule-based Success: {metrics['rule_based_count']}/{metrics['total_criteria_processed']} ({metrics['rule_based_percentage']:.1f}%)")
        print(f"⚡ Speed: {metrics['average_time_per_bidder']:.2f}s per bidder")
        print(f"🔥 Criteria Speed: {metrics['average_time_per_criteria_ms']:.1f}ms per criteria")
        
        print(f"\n📊 Performance Breakdown:")
        print(f"   Document Processing: {metrics['document_processing_seconds']:.2f}s")
        print(f"   Criteria Loading: {metrics['criteria_loading_seconds']:.2f}s")
        print(f"   Scoring: {metrics['scoring_seconds']:.2f}s")
        
        for bidder_name, bidder_data in results["results"].items():
            print(f"\n🏢 {bidder_name}")
            print(f"   Total Score: {bidder_data['total_score']:.1f}/{bidder_data['max_possible_score']:.1f}")
            print(f"   Percentage: {bidder_data['percentage_score']:.1f}%")
            print(f"   Status: {bidder_data['qualification_status']}")
            print(f"   Processing Time: {bidder_data['processing_time_seconds']:.3f}s")
            
            print("   📋 Individual Scores:")
            for score in bidder_data['individual_scores']:
                method_icon = "📏" if score['scoring_method'] == 'rule_based' else "⚠️"
                values = score.get('values_found', [])
                values_str = f" (found: {values})" if values else ""
                print(f"     {method_icon} Criteria {score['criteria_id']}: {score['awarded_marks']}/{score['max_marks']} - {score['rationale'][:60]}...{values_str}")

def main():
    """Main function"""
    print("🚀 ULTRA-FAST RFP SCORING SYSTEM")
    print("Rule-based processing only - ZERO LLM calls!")
    
    system = UltraFastScoringSystem()
    results = system.run_ultra_fast_evaluation()
    system.print_ultra_fast_results(results)
    
    if "performance_metrics" in results:
        metrics = results["performance_metrics"]
        print(f"\n🎯 PERFORMANCE SUMMARY:")
        print(f"   Original System: ~23+ minutes, 30+ LLM calls")
        print(f"   Ultra-Fast System: {metrics['total_duration_seconds']:.2f} seconds, 0 LLM calls")
        print(f"   Speed Improvement: {(23*60)/metrics['total_duration_seconds']:.0f}x faster!")

if __name__ == "__main__":
    main()
