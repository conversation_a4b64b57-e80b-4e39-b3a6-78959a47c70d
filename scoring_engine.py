"""
Automated Scoring Engine for RFP Bidder Evaluation

This module implements intelligent scoring logic that matches bidder qualifications 
against RFP criteria and assigns appropriate marks based on the marking system.
"""

import json
import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from langchain_ollama import ChatOllama
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser
from criteria_extractor import EvaluationCriteria
from bidder_analyzer import BidderAnalysis

logger = logging.getLogger(__name__)

@dataclass
class ScoringResult:
    """Structure for individual scoring result"""
    bidder_name: str
    criteria_id: str
    criteria_text: str
    extracted_info: str
    awarded_marks: float
    max_marks: float
    scoring_rationale: str
    confidence_score: float
    manual_review_required: bool
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class BidderScorecard:
    """Complete scorecard for a bidder"""
    bidder_name: str
    document_filename: str
    individual_scores: List[ScoringResult]
    total_score: float
    max_possible_score: float
    percentage_score: float
    qualification_status: str  # 'QUALIFIED', 'NOT_QUALIFIED', 'PENDING_REVIEW'
    minimum_score_required: float
    scoring_timestamp: str
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class ScoringEngine:
    """Automated scoring engine for bidder evaluation"""
    
    def __init__(self, model_name: str = "llama3.2:3b", minimum_passing_score: float = 70.0):
        self.llm = ChatOllama(model=model_name)
        self.minimum_passing_score = minimum_passing_score
        self.setup_prompts()
    
    def setup_prompts(self):
        """Setup prompt templates for scoring"""
        
        self.scoring_prompt = PromptTemplate.from_template("""
You are an expert evaluator for RFP (Request for Proposal) bidder assessment.

Evaluation Criteria:
{criteria_text}

Marking System:
{marking_system}

Maximum Marks: {max_marks}

Bidder Information Extracted:
{extracted_info}

Supporting Evidence:
{supporting_evidence}

Please evaluate the bidder's information against the criteria and assign marks according to the marking system.

Provide your response in the following JSON format:
{{
  "awarded_marks": 15.0,
  "scoring_rationale": "Detailed explanation of why these marks were awarded",
  "confidence_score": 0.85,
  "manual_review_required": false,
  "evaluation_notes": "Additional observations or concerns",
  "criteria_met": true/false,
  "missing_information": ["List any missing required information"]
}}

Scoring Guidelines:
1. Be strict and objective in evaluation
2. Award marks only for clearly demonstrated qualifications
3. If information is ambiguous or missing, award lower marks
4. Set manual_review_required to true if evaluation is uncertain
5. Provide detailed rationale for the marks awarded

Focus on:
- Exact match with criteria requirements
- Quantitative evidence (years, numbers, amounts)
- Quality and relevance of supporting documentation
- Completeness of information provided
""")

        self.marking_system_parser_prompt = PromptTemplate.from_template("""
Parse the following marking system and extract the scoring rules:

Marking System: {marking_system}
Maximum Marks: {max_marks}

Convert this into a structured scoring rule in JSON format:
{{
  "scoring_rules": [
    {{
      "condition": "Description of the condition",
      "marks": "Marks awarded for this condition",
      "range_min": "Minimum value (if applicable)",
      "range_max": "Maximum value (if applicable)"
    }}
  ],
  "default_marks": "Default marks if no condition is met",
  "evaluation_type": "range/categorical/binary"
}}

Examples:
- "10-15 yrs = 10, >15 yrs = 20" becomes range-based scoring
- "5-8 = 5, >8 = 10" becomes range-based scoring
- "Yes = 10, No = 0" becomes binary scoring
""")

    def parse_marking_system(self, marking_system: str, max_marks: str) -> Dict[str, Any]:
        """Parse marking system into structured rules"""
        try:
            chain = self.marking_system_parser_prompt | self.llm | StrOutputParser()
            response = chain.invoke({
                "marking_system": marking_system,
                "max_marks": max_marks
            })
            
            return json.loads(response)
        except Exception as e:
            logger.error(f"Error parsing marking system: {str(e)}")
            return {"scoring_rules": [], "default_marks": "0", "evaluation_type": "manual"}

    def extract_numeric_value(self, text: str) -> Optional[float]:
        """Extract numeric values from text (years, amounts, counts)"""
        # Look for patterns like "10 years", "Rs. 100 lac", "5 projects"
        patterns = [
            r'(\d+(?:\.\d+)?)\s*(?:years?|yrs?)',
            r'(?:Rs\.?\s*)?(\d+(?:\.\d+)?)\s*(?:lac|lakh|crore)',
            r'(\d+(?:\.\d+)?)\s*(?:projects?|audits?|assignments?)',
            r'(\d+(?:\.\d+)?)'  # Generic number
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return float(match.group(1))
        
        return None

    def apply_scoring_rules(self, extracted_info: str, scoring_rules: Dict[str, Any], 
                           max_marks: str) -> Tuple[float, str]:
        """Apply scoring rules to extracted information"""
        try:
            max_marks_value = float(max_marks) if max_marks.isdigit() else 0.0
        except:
            max_marks_value = 0.0
        
        if not scoring_rules.get('scoring_rules'):
            return 0.0, "No scoring rules available"
        
        # Extract numeric value from bidder information
        numeric_value = self.extract_numeric_value(extracted_info)
        
        if numeric_value is None:
            return 0.0, "No quantifiable information found"
        
        # Apply rules based on evaluation type
        evaluation_type = scoring_rules.get('evaluation_type', 'manual')
        
        if evaluation_type == 'range':
            for rule in scoring_rules['scoring_rules']:
                range_min = rule.get('range_min')
                range_max = rule.get('range_max')
                
                if range_min and range_max:
                    if float(range_min) <= numeric_value <= float(range_max):
                        marks = float(rule.get('marks', 0))
                        return marks, f"Value {numeric_value} falls in range {range_min}-{range_max}"
                elif range_min and numeric_value >= float(range_min):
                    marks = float(rule.get('marks', 0))
                    return marks, f"Value {numeric_value} meets minimum requirement of {range_min}"
        
        # Default scoring
        default_marks = float(scoring_rules.get('default_marks', 0))
        return default_marks, "Applied default scoring"

    def score_bidder_analysis(self, analysis: BidderAnalysis, criteria: EvaluationCriteria) -> ScoringResult:
        """Score a single bidder analysis against criteria"""
        
        # Parse marking system
        scoring_rules = self.parse_marking_system(criteria.marking_system, criteria.max_marks)
        
        # Try rule-based scoring first
        if criteria.marking_system and criteria.marking_system.strip():
            rule_based_marks, rule_rationale = self.apply_scoring_rules(
                analysis.extracted_information, scoring_rules, criteria.max_marks
            )
            
            # If rule-based scoring is successful, use it
            if rule_based_marks > 0:
                try:
                    max_marks_value = float(criteria.max_marks) if criteria.max_marks.isdigit() else 0.0
                except:
                    max_marks_value = 0.0
                
                return ScoringResult(
                    bidder_name=analysis.bidder_name,
                    criteria_id=analysis.criteria_id,
                    criteria_text=analysis.criteria_text,
                    extracted_info=analysis.extracted_information,
                    awarded_marks=rule_based_marks,
                    max_marks=max_marks_value,
                    scoring_rationale=f"Rule-based scoring: {rule_rationale}",
                    confidence_score=analysis.confidence_score,
                    manual_review_required=analysis.confidence_score < 0.7
                )
        
        # Fall back to LLM-based scoring
        try:
            chain = self.scoring_prompt | self.llm | StrOutputParser()
            response = chain.invoke({
                "criteria_text": criteria.criteria_text,
                "marking_system": criteria.marking_system,
                "max_marks": criteria.max_marks,
                "extracted_info": analysis.extracted_information,
                "supporting_evidence": "\n".join(analysis.supporting_evidence)
            })
            
            scoring_data = json.loads(response)
            
            try:
                max_marks_value = float(criteria.max_marks) if criteria.max_marks.isdigit() else 0.0
            except:
                max_marks_value = 0.0
            
            return ScoringResult(
                bidder_name=analysis.bidder_name,
                criteria_id=analysis.criteria_id,
                criteria_text=analysis.criteria_text,
                extracted_info=analysis.extracted_information,
                awarded_marks=float(scoring_data.get('awarded_marks', 0)),
                max_marks=max_marks_value,
                scoring_rationale=scoring_data.get('scoring_rationale', ''),
                confidence_score=float(scoring_data.get('confidence_score', 0.5)),
                manual_review_required=scoring_data.get('manual_review_required', True)
            )
            
        except Exception as e:
            logger.error(f"Error in LLM scoring: {str(e)}")
            
            # Fallback scoring
            try:
                max_marks_value = float(criteria.max_marks) if criteria.max_marks.isdigit() else 0.0
            except:
                max_marks_value = 0.0
            
            return ScoringResult(
                bidder_name=analysis.bidder_name,
                criteria_id=analysis.criteria_id,
                criteria_text=analysis.criteria_text,
                extracted_info=analysis.extracted_information,
                awarded_marks=0.0,
                max_marks=max_marks_value,
                scoring_rationale="Scoring failed - manual review required",
                confidence_score=0.0,
                manual_review_required=True
            )

    def create_bidder_scorecard(self, bidder_name: str, document_filename: str,
                               scoring_results: List[ScoringResult]) -> BidderScorecard:
        """Create comprehensive scorecard for a bidder"""
        
        total_score = sum(result.awarded_marks for result in scoring_results)
        max_possible_score = sum(result.max_marks for result in scoring_results)
        
        percentage_score = (total_score / max_possible_score * 100) if max_possible_score > 0 else 0
        
        # Determine qualification status
        if percentage_score >= self.minimum_passing_score:
            qualification_status = "QUALIFIED"
        elif any(result.manual_review_required for result in scoring_results):
            qualification_status = "PENDING_REVIEW"
        else:
            qualification_status = "NOT_QUALIFIED"
        
        return BidderScorecard(
            bidder_name=bidder_name,
            document_filename=document_filename,
            individual_scores=scoring_results,
            total_score=total_score,
            max_possible_score=max_possible_score,
            percentage_score=percentage_score,
            qualification_status=qualification_status,
            minimum_score_required=self.minimum_passing_score,
            scoring_timestamp=datetime.now().isoformat()
        )

    def score_all_bidders(self, analysis_results: Dict[str, Any], 
                         criteria_list: List[EvaluationCriteria]) -> Dict[str, BidderScorecard]:
        """Score all bidders and create scorecards"""
        
        scorecards = {}
        
        for bidder_name, analyses in analysis_results['bidder_analyses'].items():
            logger.info(f"Scoring bidder: {bidder_name}")
            
            scoring_results = []
            
            # Create mapping of criteria by ID for quick lookup
            criteria_map = {criteria.serial_number: criteria for criteria in criteria_list}
            
            for analysis_data in analyses:
                analysis = BidderAnalysis(**analysis_data)
                criteria = criteria_map.get(analysis.criteria_id)
                
                if criteria:
                    scoring_result = self.score_bidder_analysis(analysis, criteria)
                    scoring_results.append(scoring_result)
            
            # Get document filename from analysis results
            document_filename = analyses[0]['document_filename'] if analyses else 'Unknown'
            
            # Create scorecard
            scorecard = self.create_bidder_scorecard(bidder_name, document_filename, scoring_results)
            scorecards[bidder_name] = scorecard
        
        return scorecards

    def save_scorecards(self, scorecards: Dict[str, BidderScorecard], 
                       output_file: str = "bidder_scorecards.json"):
        """Save scorecards to JSON file"""
        serializable_scorecards = {
            name: scorecard.to_dict() for name, scorecard in scorecards.items()
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_scorecards, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Scorecards saved to: {output_file}")

    def generate_summary_report(self, scorecards: Dict[str, BidderScorecard]) -> Dict[str, Any]:
        """Generate summary report of all bidders"""
        
        qualified_bidders = [name for name, card in scorecards.items() 
                           if card.qualification_status == "QUALIFIED"]
        
        pending_review = [name for name, card in scorecards.items() 
                         if card.qualification_status == "PENDING_REVIEW"]
        
        not_qualified = [name for name, card in scorecards.items() 
                        if card.qualification_status == "NOT_QUALIFIED"]
        
        # Calculate statistics
        scores = [card.percentage_score for card in scorecards.values()]
        avg_score = sum(scores) / len(scores) if scores else 0
        
        return {
            "evaluation_summary": {
                "total_bidders": len(scorecards),
                "qualified_bidders": len(qualified_bidders),
                "pending_review": len(pending_review),
                "not_qualified": len(not_qualified),
                "average_score": round(avg_score, 2),
                "minimum_passing_score": self.minimum_passing_score
            },
            "qualified_bidders": qualified_bidders,
            "pending_review": pending_review,
            "not_qualified": not_qualified,
            "bidder_rankings": sorted(
                [(name, card.percentage_score) for name, card in scorecards.items()],
                key=lambda x: x[1], reverse=True
            )
        }

if __name__ == "__main__":
    # Example usage
    from document_processor import DocumentProcessor
    from criteria_extractor import CriteriaExtractor
    from bidder_analyzer import BidderAnalyzer
    
    # Load analysis results
    with open("bidder_analysis_results.json", 'r', encoding='utf-8') as f:
        analysis_results = json.load(f)
    
    # Load criteria
    extractor = CriteriaExtractor()
    criteria_list = extractor.load_existing_criteria()
    
    # Score all bidders
    scoring_engine = ScoringEngine()
    scorecards = scoring_engine.score_all_bidders(analysis_results, criteria_list)
    scoring_engine.save_scorecards(scorecards)
    
    # Generate summary
    summary = scoring_engine.generate_summary_report(scorecards)
    
    print("Scoring Summary:")
    print(f"Total Bidders: {summary['evaluation_summary']['total_bidders']}")
    print(f"Qualified: {summary['evaluation_summary']['qualified_bidders']}")
    print(f"Pending Review: {summary['evaluation_summary']['pending_review']}")
    print(f"Not Qualified: {summary['evaluation_summary']['not_qualified']}")
    print(f"Average Score: {summary['evaluation_summary']['average_score']}%")
