#!/usr/bin/env python3
"""
Quick test script to validate the RFP scoring system
"""

import os
import sys

def test_document_processing():
    """Test document processing"""
    print("Testing document processing...")
    try:
        from document_processor import DocumentProcessor
        
        dp = DocumentProcessor()
        docs = dp.process_all_documents()
        
        print(f"✓ RFP documents: {len(docs['rfp'])}")
        for doc in docs['rfp']:
            print(f"  - {doc.filename} ({doc.metadata['word_count']} words)")
        
        print(f"✓ Bidder documents: {len(docs['bidder'])}")
        for doc in docs['bidder']:
            print(f"  - {doc.filename} ({doc.metadata['word_count']} words)")
        
        return True
    except Exception as e:
        print(f"✗ Document processing failed: {e}")
        return False

def test_criteria_loading():
    """Test criteria loading"""
    print("\nTesting criteria loading...")
    try:
        from criteria_extractor import CriteriaExtractor
        
        extractor = CriteriaExtractor()
        criteria = extractor.load_existing_criteria()
        
        print(f"✓ Loaded {len(criteria)} criteria from combined_table.txt")
        for i, criterion in enumerate(criteria[:3]):  # Show first 3
            print(f"  {i+1}. {criterion.criteria_text[:60]}...")
        
        return True
    except Exception as e:
        print(f"✗ Criteria loading failed: {e}")
        return False

def test_system_initialization():
    """Test system initialization"""
    print("\nTesting system initialization...")
    try:
        from rfp_scoring_system import RFPScoringSystem
        
        system = RFPScoringSystem()
        print("✓ System initialized successfully")
        
        # Test document processing
        processed_docs = system.process_documents()
        print(f"✓ Processed {len(processed_docs['rfp'])} RFP and {len(processed_docs['bidder'])} bidder documents")
        
        return True
    except Exception as e:
        print(f"✗ System initialization failed: {e}")
        return False

def check_requirements():
    """Check if all requirements are met"""
    print("Checking requirements...")
    
    required_files = [
        "documents/QCBS 1.pdf",
        "documents/Dummy Bidder Document1.pdf", 
        "combined_table.txt"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"✗ {file} (missing)")
            missing_files.append(file)
    
    return len(missing_files) == 0

def main():
    """Main test function"""
    print("RFP Scoring System - Quick Test")
    print("=" * 40)
    
    # Check requirements
    if not check_requirements():
        print("\n⚠ Some required files are missing. Please ensure all documents are in place.")
        return False
    
    # Test components
    tests = [
        test_document_processing,
        test_criteria_loading,
        test_system_initialization
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 40)
    print("Test Summary:")
    passed = sum(results)
    total = len(results)
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed! The system is ready to use.")
        print("\nTo run the complete evaluation:")
        print("python rfp_scoring_system.py")
    else:
        print("✗ Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
