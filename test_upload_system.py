#!/usr/bin/env python3
"""
Test script for the upload-based scoring system
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add current directory to path
sys.path.append('.')

def test_upload_based_system():
    """Test the upload-based scoring system"""
    print("🧪 Testing Upload-Based Scoring System")
    print("="*50)
    
    try:
        # Import the new system
        from app import UploadBasedScoringSystem
        
        # Create test directories
        test_dir = "test_upload_files"
        bidder_dir = os.path.join(test_dir, "bidder")
        os.makedirs(bidder_dir, exist_ok=True)
        
        # Copy existing bidder documents to test directory
        existing_docs = Path("documents")
        if existing_docs.exists():
            for doc_file in existing_docs.glob("*.pdf"):
                if "bidder" in doc_file.name.lower() or "dummy" in doc_file.name.lower():
                    shutil.copy2(doc_file, bidder_dir)
                    print(f"✅ Copied test file: {doc_file.name}")
        
        # Test the system
        print(f"\n🚀 Testing with files in: {bidder_dir}")
        
        scoring_system = UploadBasedScoringSystem()
        results = scoring_system.process_uploaded_files(bidder_dir, "sample_criteria.json")
        
        if "error" in results:
            print(f"❌ Test failed: {results['error']}")
            return False
        
        print("✅ Test passed!")
        print(f"📊 Results: {results['total_bidders']} bidders evaluated")
        
        for bidder_name, data in results["results"].items():
            print(f"  - {bidder_name}: {data['percentage_score']:.1f}% ({data['qualification_status']})")
        
        # Cleanup
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return False

def test_criteria_loading():
    """Test criteria loading from different formats"""
    print("\n🧪 Testing Criteria Loading")
    print("="*30)
    
    try:
        from app import UploadBasedScoringSystem
        
        system = UploadBasedScoringSystem()
        
        # Test JSON criteria loading
        if os.path.exists("sample_criteria.json"):
            criteria = system.load_criteria_from_uploaded_file("sample_criteria.json")
            print(f"✅ JSON criteria loaded: {len(criteria)} criteria")
        else:
            print("⚠️ sample_criteria.json not found")

        # Test PDF criteria loading
        pdf_files = [f for f in os.listdir(".") if f.endswith('.pdf') and 'rfp' in f.lower()]
        if pdf_files:
            test_pdf = pdf_files[0]
            print(f"🧪 Testing PDF criteria extraction with: {test_pdf}")
            criteria = system.load_criteria_from_uploaded_file(test_pdf)
            print(f"✅ PDF criteria loaded: {len(criteria)} criteria")
        else:
            print("⚠️ No RFP PDF files found for testing")
        
        # Test fallback to existing criteria
        criteria = system.load_criteria_from_uploaded_file("nonexistent.json")
        print(f"✅ Fallback criteria handling: {len(criteria)} criteria")
        
        return True
        
    except Exception as e:
        print(f"❌ Criteria loading test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🎯 UPLOAD-BASED SCORING SYSTEM TESTS")
    print("="*60)
    
    tests = [
        test_criteria_loading,
        test_upload_based_system
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
    
    print(f"\n📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! The upload-based system is working correctly.")
        print("\n🚀 You can now run the Streamlit app:")
        print("   streamlit run app.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
