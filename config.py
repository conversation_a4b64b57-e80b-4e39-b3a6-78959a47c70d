"""
Configuration System for RFP Scoring System

This module provides configuration management for different RFP types,
scoring weights, model parameters, and system settings.
"""

import json
import os
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

@dataclass
class ModelConfig:
    """Configuration for LLM models"""
    name: str
    temperature: float = 0.1
    max_tokens: int = 2000
    timeout: int = 60
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class ScoringConfig:
    """Configuration for scoring parameters"""
    minimum_passing_score: float = 70.0
    confidence_threshold: float = 0.7
    manual_review_threshold: float = 0.5
    scoring_weights: Dict[str, float] = None
    
    def __post_init__(self):
        if self.scoring_weights is None:
            self.scoring_weights = {
                "experience": 1.0,
                "financial": 1.0,
                "technical": 1.0,
                "methodology": 1.0
            }
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class ProcessingConfig:
    """Configuration for document processing"""
    supported_formats: List[str] = None
    chunk_size: int = 600
    chunk_overlap: int = 100
    similarity_search_k: int = 3
    max_document_size_mb: int = 50
    
    def __post_init__(self):
        if self.supported_formats is None:
            self.supported_formats = ['.pdf', '.docx', '.txt', '.doc']
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class RFPTypeConfig:
    """Configuration for specific RFP types"""
    rfp_type: str
    criteria_patterns: List[str]
    required_sections: List[str]
    scoring_methodology: str
    special_requirements: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.special_requirements is None:
            self.special_requirements = {}
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class ConfigManager:
    """Configuration manager for the RFP scoring system"""
    
    def __init__(self, config_file: str = "system_config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def get_default_config(self) -> Dict[str, Any]:
        """Get default system configuration"""
        return {
            "system": {
                "version": "1.0.0",
                "documents_directory": "documents",
                "output_directory": "reports",
                "log_level": "INFO",
                "enable_caching": True,
                "cache_directory": "cache"
            },
            "models": {
                "primary_llm": ModelConfig(
                    name="llama3.2:3b",
                    temperature=0.1,
                    max_tokens=2000
                ).to_dict(),
                "embedding_model": {
                    "name": "sentence-transformers/all-MiniLM-L6-v2",
                    "cache_folder": "embeddings_cache"
                },
                "fallback_llm": ModelConfig(
                    name="llama3.2:1b",
                    temperature=0.2
                ).to_dict()
            },
            "scoring": ScoringConfig().to_dict(),
            "processing": ProcessingConfig().to_dict(),
            "rfp_types": {
                "technical_services": RFPTypeConfig(
                    rfp_type="technical_services",
                    criteria_patterns=[
                        "technical evaluation criteria",
                        "experience requirements",
                        "team composition",
                        "methodology"
                    ],
                    required_sections=[
                        "company_profile",
                        "experience",
                        "team_details",
                        "methodology"
                    ],
                    scoring_methodology="weighted_average"
                ).to_dict(),
                "consulting": RFPTypeConfig(
                    rfp_type="consulting",
                    criteria_patterns=[
                        "consultant qualifications",
                        "project experience",
                        "approach and methodology",
                        "team expertise"
                    ],
                    required_sections=[
                        "firm_profile",
                        "key_personnel",
                        "relevant_experience",
                        "proposed_approach"
                    ],
                    scoring_methodology="criteria_based"
                ).to_dict(),
                "audit_services": RFPTypeConfig(
                    rfp_type="audit_services",
                    criteria_patterns=[
                        "firm registration",
                        "audit experience",
                        "government projects",
                        "team qualifications"
                    ],
                    required_sections=[
                        "registration_certificate",
                        "audit_experience",
                        "financial_statements",
                        "team_composition"
                    ],
                    scoring_methodology="points_based",
                    special_requirements={
                        "minimum_experience_years": 10,
                        "required_certifications": ["ICAI"],
                        "government_project_requirement": 5
                    }
                ).to_dict()
            },
            "extraction_prompts": {
                "criteria_extraction": {
                    "system_prompt": "You are an expert at analyzing RFP documents and extracting evaluation criteria.",
                    "temperature": 0.1,
                    "max_retries": 3
                },
                "bidder_analysis": {
                    "system_prompt": "You are an expert at analyzing bidder proposal documents.",
                    "temperature": 0.1,
                    "max_retries": 3
                },
                "scoring": {
                    "system_prompt": "You are an expert evaluator for RFP bidder assessment.",
                    "temperature": 0.05,
                    "max_retries": 2
                }
            },
            "output_formats": {
                "reports": ["json", "csv", "excel"],
                "scorecards": ["json", "pdf"],
                "summary": ["json", "txt"]
            }
        }
    
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from file or create default"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading config: {e}. Using default configuration.")
                return self.get_default_config()
        else:
            config = self.get_default_config()
            self.save_config(config)
            return config
    
    def save_config(self, config: Optional[Dict[str, Any]] = None):
        """Save configuration to file"""
        if config is None:
            config = self.config
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """Get configuration value using dot notation (e.g., 'models.primary_llm.name')"""
        keys = key_path.split('.')
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def set(self, key_path: str, value: Any):
        """Set configuration value using dot notation"""
        keys = key_path.split('.')
        config = self.config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
        self.save_config()
    
    def get_model_config(self, model_type: str = "primary_llm") -> ModelConfig:
        """Get model configuration"""
        model_data = self.get(f"models.{model_type}")
        if model_data:
            return ModelConfig(**model_data)
        return ModelConfig(name="llama3.2:3b")
    
    def get_scoring_config(self) -> ScoringConfig:
        """Get scoring configuration"""
        scoring_data = self.get("scoring")
        if scoring_data:
            return ScoringConfig(**scoring_data)
        return ScoringConfig()
    
    def get_processing_config(self) -> ProcessingConfig:
        """Get processing configuration"""
        processing_data = self.get("processing")
        if processing_data:
            return ProcessingConfig(**processing_data)
        return ProcessingConfig()
    
    def get_rfp_type_config(self, rfp_type: str) -> Optional[RFPTypeConfig]:
        """Get RFP type specific configuration"""
        rfp_data = self.get(f"rfp_types.{rfp_type}")
        if rfp_data:
            return RFPTypeConfig(**rfp_data)
        return None
    
    def add_rfp_type(self, rfp_type_config: RFPTypeConfig):
        """Add new RFP type configuration"""
        self.config["rfp_types"][rfp_type_config.rfp_type] = rfp_type_config.to_dict()
        self.save_config()
    
    def update_scoring_weights(self, weights: Dict[str, float]):
        """Update scoring weights"""
        current_scoring = self.get_scoring_config()
        current_scoring.scoring_weights.update(weights)
        self.set("scoring.scoring_weights", current_scoring.scoring_weights)
    
    def get_supported_rfp_types(self) -> List[str]:
        """Get list of supported RFP types"""
        return list(self.get("rfp_types", {}).keys())
    
    def validate_config(self) -> List[str]:
        """Validate configuration and return list of issues"""
        issues = []
        
        # Check required sections
        required_sections = ["system", "models", "scoring", "processing"]
        for section in required_sections:
            if section not in self.config:
                issues.append(f"Missing required section: {section}")
        
        # Validate model configuration
        primary_llm = self.get("models.primary_llm.name")
        if not primary_llm:
            issues.append("Primary LLM model name not specified")
        
        # Validate scoring configuration
        min_score = self.get("scoring.minimum_passing_score")
        if min_score is None or not (0 <= min_score <= 100):
            issues.append("Invalid minimum passing score")
        
        # Validate directories
        docs_dir = self.get("system.documents_directory")
        if docs_dir and not os.path.exists(docs_dir):
            issues.append(f"Documents directory does not exist: {docs_dir}")
        
        return issues
    
    def create_directories(self):
        """Create necessary directories based on configuration"""
        directories = [
            self.get("system.documents_directory"),
            self.get("system.output_directory"),
            self.get("system.cache_directory")
        ]
        
        for directory in directories:
            if directory:
                Path(directory).mkdir(parents=True, exist_ok=True)

# Global configuration instance
config_manager = ConfigManager()

def get_config() -> ConfigManager:
    """Get global configuration manager instance"""
    return config_manager

def load_custom_config(config_file: str) -> ConfigManager:
    """Load custom configuration from file"""
    return ConfigManager(config_file)

if __name__ == "__main__":
    # Example usage and configuration validation
    config = get_config()
    
    print("Current Configuration:")
    print(f"Primary LLM: {config.get('models.primary_llm.name')}")
    print(f"Minimum Passing Score: {config.get('scoring.minimum_passing_score')}%")
    print(f"Documents Directory: {config.get('system.documents_directory')}")
    
    print("\nSupported RFP Types:")
    for rfp_type in config.get_supported_rfp_types():
        print(f"  - {rfp_type}")
    
    print("\nValidating configuration...")
    issues = config.validate_config()
    if issues:
        print("Configuration issues found:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("Configuration is valid!")
    
    # Create necessary directories
    config.create_directories()
    print("Directories created successfully!")
