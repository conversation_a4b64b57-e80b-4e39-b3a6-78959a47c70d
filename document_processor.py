"""
Document Processing Pipeline for RFP Bidder Scoring System

This module handles extraction and processing of both RFP documents and bidder documents,
supporting multiple file formats (PDF, DOCX, TXT) and providing structured data extraction.
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, asdict
from pathlib import Path
import pdfplumber
import pandas as pd
from docx import Document
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class EvaluationCriteria:
    """Structure for RFP evaluation criteria"""
    serial_number: str
    criteria_text: str
    relevant_document: str
    marking_system: str
    max_marks: Union[int, str]
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class ProcessedDocument:
    """Structure for processed document information"""
    filename: str
    file_path: str
    document_type: str  # 'rfp' or 'bidder'
    content: str
    metadata: Dict[str, Any]
    extraction_method: str
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class DocumentProcessor:
    """Main document processing class"""
    
    def __init__(self, documents_dir: str = "documents"):
        self.documents_dir = Path(documents_dir)
        self.supported_formats = ['.pdf', '.docx', '.txt', '.doc']
        
    def extract_text_from_pdf(self, file_path: Path) -> str:
        """Extract text from PDF files using pdfplumber"""
        try:
            text_content = []
            with pdfplumber.open(file_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text_content.append(page_text)
            return "\n\n".join(text_content)
        except Exception as e:
            logger.error(f"Error extracting text from PDF {file_path}: {str(e)}")
            return ""
    
    def extract_text_from_docx(self, file_path: Path) -> str:
        """Extract text from DOCX files"""
        try:
            doc = Document(file_path)
            text_content = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)
            return "\n\n".join(text_content)
        except Exception as e:
            logger.error(f"Error extracting text from DOCX {file_path}: {str(e)}")
            return ""
    
    def extract_text_from_txt(self, file_path: Path) -> str:
        """Extract text from TXT files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read()
        except Exception as e:
            logger.error(f"Error extracting text from TXT {file_path}: {str(e)}")
            return ""
    
    def extract_text(self, file_path: Path) -> tuple[str, str]:
        """Extract text from supported file formats"""
        file_extension = file_path.suffix.lower()
        extraction_method = ""
        
        if file_extension == '.pdf':
            text = self.extract_text_from_pdf(file_path)
            extraction_method = "pdfplumber"
        elif file_extension == '.docx':
            text = self.extract_text_from_docx(file_path)
            extraction_method = "python-docx"
        elif file_extension == '.txt':
            text = self.extract_text_from_txt(file_path)
            extraction_method = "direct_read"
        else:
            logger.warning(f"Unsupported file format: {file_extension}")
            return "", "unsupported"
        
        return text, extraction_method
    
    def extract_tables_from_pdf(self, file_path: Path) -> List[List[List[str]]]:
        """Extract tables from PDF files"""
        try:
            all_tables = []
            with pdfplumber.open(file_path) as pdf:
                for page in pdf.pages:
                    tables = page.extract_tables()
                    if tables:
                        all_tables.extend(tables)
            return all_tables
        except Exception as e:
            logger.error(f"Error extracting tables from PDF {file_path}: {str(e)}")
            return []
    
    def classify_document_type(self, filename: str, content: str) -> str:
        """Classify document as RFP or bidder document based on filename and content"""
        filename_lower = filename.lower()
        content_lower = content.lower()
        
        # RFP indicators
        rfp_keywords = ['rfp', 'request for proposal', 'tender', 'evaluation criteria', 
                       'technical evaluation', 'marking system', 'qcbs']
        
        # Bidder indicators  
        bidder_keywords = ['bidder', 'proposal', 'company profile', 'experience', 
                          'qualification', 'team composition', 'methodology']
        
        rfp_score = sum(1 for keyword in rfp_keywords if keyword in filename_lower or keyword in content_lower)
        bidder_score = sum(1 for keyword in bidder_keywords if keyword in filename_lower or keyword in content_lower)
        
        if rfp_score > bidder_score:
            return 'rfp'
        elif bidder_score > rfp_score:
            return 'bidder'
        else:
            # Default classification based on filename patterns
            if any(term in filename_lower for term in ['rfp', 'tender', 'qcbs']):
                return 'rfp'
            else:
                return 'bidder'
    
    def process_single_document(self, file_path: Path) -> Optional[ProcessedDocument]:
        """Process a single document and return structured information"""
        if not file_path.exists():
            logger.error(f"File not found: {file_path}")
            return None
        
        if file_path.suffix.lower() not in self.supported_formats:
            logger.warning(f"Unsupported file format: {file_path}")
            return None
        
        # Extract text content
        content, extraction_method = self.extract_text(file_path)
        if not content:
            logger.warning(f"No content extracted from: {file_path}")
            return None
        
        # Classify document type
        doc_type = self.classify_document_type(file_path.name, content)
        
        # Create metadata
        metadata = {
            'file_size': file_path.stat().st_size,
            'file_extension': file_path.suffix,
            'word_count': len(content.split()),
            'char_count': len(content)
        }
        
        return ProcessedDocument(
            filename=file_path.name,
            file_path=str(file_path),
            document_type=doc_type,
            content=content,
            metadata=metadata,
            extraction_method=extraction_method
        )
    
    def process_all_documents(self) -> Dict[str, List[ProcessedDocument]]:
        """Process all documents in the documents directory"""
        if not self.documents_dir.exists():
            logger.error(f"Documents directory not found: {self.documents_dir}")
            return {'rfp': [], 'bidder': []}
        
        processed_docs = {'rfp': [], 'bidder': []}
        
        for file_path in self.documents_dir.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                logger.info(f"Processing: {file_path.name}")
                processed_doc = self.process_single_document(file_path)
                if processed_doc:
                    processed_docs[processed_doc.document_type].append(processed_doc)
        
        logger.info(f"Processed {len(processed_docs['rfp'])} RFP documents and {len(processed_docs['bidder'])} bidder documents")
        return processed_docs
    
    def save_processed_documents(self, processed_docs: Dict[str, List[ProcessedDocument]], 
                               output_file: str = "processed_documents.json"):
        """Save processed documents to JSON file"""
        serializable_docs = {
            'rfp': [doc.to_dict() for doc in processed_docs['rfp']],
            'bidder': [doc.to_dict() for doc in processed_docs['bidder']]
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_docs, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Processed documents saved to: {output_file}")

if __name__ == "__main__":
    # Example usage
    processor = DocumentProcessor()
    processed_docs = processor.process_all_documents()
    processor.save_processed_documents(processed_docs)
    
    # Print summary
    print(f"RFP Documents: {len(processed_docs['rfp'])}")
    for doc in processed_docs['rfp']:
        print(f"  - {doc.filename} ({doc.metadata['word_count']} words)")
    
    print(f"Bidder Documents: {len(processed_docs['bidder'])}")
    for doc in processed_docs['bidder']:
        print(f"  - {doc.filename} ({doc.metadata['word_count']} words)")
