"""
Bidder Document Analysis Engine

This module analyzes bidder documents against specific RFP criteria,
extracting relevant information for each evaluation point using open source LLMs.
"""

import json
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from langchain_ollama import ChatOllama
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS
from langchain.text_splitter import RecursiveCharacterTextSplitter
from document_processor import ProcessedDocument
from criteria_extractor import EvaluationCriteria

logger = logging.getLogger(__name__)

@dataclass
class BidderAnalysis:
    """Structure for bidder analysis results"""
    bidder_name: str
    document_filename: str
    criteria_id: str
    criteria_text: str
    extracted_information: str
    confidence_score: float
    supporting_evidence: List[str]
    analysis_method: str
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class BidderProfile:
    """Structure for comprehensive bidder profile"""
    bidder_name: str
    document_filename: str
    company_info: Dict[str, Any]
    experience_info: Dict[str, Any]
    financial_info: Dict[str, Any]
    team_info: Dict[str, Any]
    methodology_info: Dict[str, Any]
    raw_content: str
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class BidderAnalyzer:
    """Analyze bidder documents against RFP criteria"""
    
    def __init__(self, model_name: str = "llama3.2:3b"):
        self.llm = ChatOllama(model=model_name)
        self.embeddings = HuggingFaceEmbeddings(model_name="sentence-transformers/all-MiniLM-L6-v2")
        self.text_splitter = RecursiveCharacterTextSplitter(chunk_size=600, chunk_overlap=100)
        self.setup_prompts()
    
    def setup_prompts(self):
        """Setup prompt templates for bidder analysis"""
        
        self.information_extraction_prompt = PromptTemplate.from_template("""
You are an expert at analyzing bidder proposal documents and extracting specific information.

Bidder Document Content:
{document_content}

Evaluation Criteria:
{criteria_text}

Required Information:
{required_info}

Please analyze the bidder document and extract information relevant to the evaluation criteria.

Provide your response in the following JSON format:
{{
  "extracted_information": "The specific information found that relates to the criteria",
  "confidence_score": 0.85,
  "supporting_evidence": ["Direct quotes or references from the document"],
  "analysis_notes": "Additional context or observations",
  "meets_criteria": true/false,
  "gaps_identified": ["Any missing information or requirements not met"]
}}

Focus on:
1. Exact information that matches the criteria
2. Quantitative data (years of experience, number of projects, financial figures)
3. Qualifications and certifications
4. Team composition and expertise
5. Methodology and approach details

Be precise and provide confidence scores based on how clearly the information is stated in the document.
""")

        self.company_profile_prompt = PromptTemplate.from_template("""
Extract comprehensive company profile information from the following bidder document:

Document Content:
{document_content}

Extract and structure the following information in JSON format:
{{
  "company_name": "",
  "registration_details": {{
    "registration_authority": "",
    "registration_number": "",
    "registration_date": "",
    "years_in_business": ""
  }},
  "contact_information": {{
    "address": "",
    "phone": "",
    "email": "",
    "website": ""
  }},
  "business_type": "",
  "specializations": [],
  "certifications": [],
  "key_personnel": []
}}

Extract only information that is explicitly mentioned in the document.
""")

        self.experience_extraction_prompt = PromptTemplate.from_template("""
Extract experience and project information from the bidder document:

Document Content:
{document_content}

Extract and structure experience information in JSON format:
{{
  "total_experience_years": "",
  "relevant_projects": [
    {{
      "project_name": "",
      "client": "",
      "duration": "",
      "value": "",
      "description": "",
      "completion_date": ""
    }}
  ],
  "government_projects": [],
  "psu_projects": [],
  "similar_assignments": [],
  "project_statistics": {{
    "total_projects": "",
    "government_projects_count": "",
    "average_project_value": ""
  }}
}}

Focus on projects relevant to the RFP requirements.
""")

    def create_vector_store(self, document_content: str) -> FAISS:
        """Create vector store from document content for similarity search"""
        chunks = self.text_splitter.create_documents([document_content])
        vector_store = FAISS.from_documents(chunks, self.embeddings)
        return vector_store

    def extract_relevant_sections(self, document_content: str, criteria_text: str, k: int = 3) -> List[str]:
        """Extract relevant sections from document using similarity search"""
        try:
            vector_store = self.create_vector_store(document_content)
            retriever = vector_store.as_retriever(search_type="similarity", search_kwargs={"k": k})
            relevant_docs = retriever.invoke(criteria_text)
            return [doc.page_content for doc in relevant_docs]
        except Exception as e:
            logger.error(f"Error in similarity search: {str(e)}")
            return [document_content[:2000]]  # Fallback to first 2000 chars

    def analyze_against_criteria(self, bidder_doc: ProcessedDocument,
                                criteria: EvaluationCriteria) -> BidderAnalysis:
        """Analyze bidder document against specific criteria"""

        # Extract relevant sections using similarity search
        relevant_sections = self.extract_relevant_sections(
            bidder_doc.content,
            criteria.criteria_text
        )

        # Combine relevant sections
        relevant_content = "\n\n".join(relevant_sections)

        try:
            # Use LLM to extract information
            chain = self.information_extraction_prompt | self.llm | StrOutputParser()
            response = chain.invoke({
                "document_content": relevant_content,
                "criteria_text": criteria.criteria_text,
                "required_info": criteria.relevant_document
            })

            # Clean and parse response
            try:
                # Try to extract JSON from response if it's embedded in text
                cleaned_response = self.extract_json_from_response(response)
                analysis_data = json.loads(cleaned_response)

                return BidderAnalysis(
                    bidder_name=self.extract_bidder_name(bidder_doc.filename),
                    document_filename=bidder_doc.filename,
                    criteria_id=criteria.serial_number,
                    criteria_text=criteria.criteria_text,
                    extracted_information=analysis_data.get('extracted_information', ''),
                    confidence_score=float(analysis_data.get('confidence_score', 0.5)),
                    supporting_evidence=analysis_data.get('supporting_evidence', []),
                    analysis_method='llm_with_similarity_search'
                )

            except (json.JSONDecodeError, ValueError) as e:
                logger.warning(f"Failed to parse JSON response, using fallback: {str(e)}")
                return self.create_fallback_analysis(bidder_doc, criteria, response)

        except Exception as e:
            logger.error(f"Error in criteria analysis: {str(e)}")
            return self.create_fallback_analysis(bidder_doc, criteria, "Analysis failed")

    def create_fallback_analysis(self, bidder_doc: ProcessedDocument, 
                                criteria: EvaluationCriteria, response: str) -> BidderAnalysis:
        """Create fallback analysis when JSON parsing fails"""
        return BidderAnalysis(
            bidder_name=self.extract_bidder_name(bidder_doc.filename),
            document_filename=bidder_doc.filename,
            criteria_id=criteria.serial_number,
            criteria_text=criteria.criteria_text,
            extracted_information=response[:500],  # First 500 chars
            confidence_score=0.3,
            supporting_evidence=[],
            analysis_method='fallback'
        )

    def extract_json_from_response(self, response: str) -> str:
        """Extract JSON from LLM response that might contain extra text"""
        # Try to find JSON block in the response
        import re

        # Look for JSON block between ```json and ``` or { and }
        json_patterns = [
            r'```json\s*(\{.*?\})\s*```',
            r'```\s*(\{.*?\})\s*```',
            r'(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})',
        ]

        for pattern in json_patterns:
            match = re.search(pattern, response, re.DOTALL)
            if match:
                return match.group(1).strip()

        # If no JSON block found, try to clean the response
        response = response.strip()
        if response.startswith('{') and response.endswith('}'):
            return response

        # Last resort: create a simple JSON structure from the response
        return json.dumps({
            "extracted_information": response[:500],
            "confidence_score": 0.3,
            "supporting_evidence": [],
            "analysis_notes": "Fallback parsing used",
            "meets_criteria": False,
            "gaps_identified": ["Unable to parse structured response"]
        })

    def extract_bidder_name(self, filename: str) -> str:
        """Extract bidder name from filename"""
        # Remove file extension and common words
        name = filename.replace('.pdf', '').replace('.docx', '').replace('.txt', '')
        name = name.replace('Bidder', '').replace('Document', '').replace('Proposal', '')
        return name.strip()

    def create_comprehensive_profile(self, bidder_doc: ProcessedDocument) -> BidderProfile:
        """Create comprehensive bidder profile"""
        
        # Extract company information
        company_chain = self.company_profile_prompt | self.llm | StrOutputParser()
        company_response = company_chain.invoke({"document_content": bidder_doc.content[:3000]})
        
        # Extract experience information
        experience_chain = self.experience_extraction_prompt | self.llm | StrOutputParser()
        experience_response = experience_chain.invoke({"document_content": bidder_doc.content})
        
        try:
            company_info = json.loads(company_response)
        except json.JSONDecodeError:
            company_info = {"error": "Failed to parse company information"}
        
        try:
            experience_info = json.loads(experience_response)
        except json.JSONDecodeError:
            experience_info = {"error": "Failed to parse experience information"}
        
        return BidderProfile(
            bidder_name=self.extract_bidder_name(bidder_doc.filename),
            document_filename=bidder_doc.filename,
            company_info=company_info,
            experience_info=experience_info,
            financial_info={},  # To be implemented
            team_info={},       # To be implemented
            methodology_info={}, # To be implemented
            raw_content=bidder_doc.content
        )

    def analyze_multiple_bidders(self, bidder_docs: List[ProcessedDocument], 
                                criteria_list: List[EvaluationCriteria]) -> Dict[str, Any]:
        """Analyze multiple bidder documents against all criteria"""
        
        results = {
            'bidder_analyses': {},
            'bidder_profiles': {},
            'summary': {
                'total_bidders': len(bidder_docs),
                'total_criteria': len(criteria_list),
                'analysis_timestamp': None
            }
        }
        
        for bidder_doc in bidder_docs:
            bidder_name = self.extract_bidder_name(bidder_doc.filename)
            logger.info(f"Analyzing bidder: {bidder_name}")
            
            # Create comprehensive profile
            profile = self.create_comprehensive_profile(bidder_doc)
            results['bidder_profiles'][bidder_name] = profile.to_dict()
            
            # Analyze against each criteria
            bidder_analyses = []
            for criteria in criteria_list:
                if criteria.criteria_text.strip():  # Skip empty criteria
                    analysis = self.analyze_against_criteria(bidder_doc, criteria)
                    bidder_analyses.append(analysis.to_dict())
            
            results['bidder_analyses'][bidder_name] = bidder_analyses
        
        return results

    def save_analysis_results(self, results: Dict[str, Any], 
                             output_file: str = "bidder_analysis_results.json"):
        """Save analysis results to JSON file"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Analysis results saved to: {output_file}")

if __name__ == "__main__":
    # Example usage
    from document_processor import DocumentProcessor
    from criteria_extractor import CriteriaExtractor
    
    # Process documents
    processor = DocumentProcessor()
    processed_docs = processor.process_all_documents()
    
    # Load criteria
    extractor = CriteriaExtractor()
    criteria_list = extractor.load_existing_criteria()
    
    # Analyze bidders
    analyzer = BidderAnalyzer()
    results = analyzer.analyze_multiple_bidders(processed_docs['bidder'], criteria_list)
    analyzer.save_analysis_results(results)
    
    # Print summary
    print(f"Analyzed {results['summary']['total_bidders']} bidders against {results['summary']['total_criteria']} criteria")
    for bidder_name in results['bidder_analyses'].keys():
        analyses_count = len(results['bidder_analyses'][bidder_name])
        print(f"  {bidder_name}: {analyses_count} criteria analyses")
