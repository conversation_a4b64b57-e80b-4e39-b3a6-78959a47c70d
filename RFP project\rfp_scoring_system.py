"""
RFP Bidder Scoring System - Main Application

This is the main application that orchestrates the entire RFP bidder scoring process,
providing batch processing capabilities with progress tracking.
"""

import os
import json
import logging
import argparse
from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime
from tqdm import tqdm
import pandas as pd

from document_processor import DocumentProcessor, ProcessedDocument
from criteria_extractor import CriteriaExtractor, EvaluationCriteria
from bidder_analyzer import BidderAnalyzer, BidderAnalysis
from scoring_engine import ScoringEngine, BidderScorecard

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('rfp_scoring.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RFPScoringSystem:
    """Main RFP Scoring System orchestrator"""
    
    def __init__(self, documents_dir: str = "documents", 
                 model_name: str = "llama3.2:3b",
                 minimum_passing_score: float = 70.0):
        
        self.documents_dir = documents_dir
        self.model_name = model_name
        self.minimum_passing_score = minimum_passing_score
        
        # Initialize components
        self.document_processor = DocumentProcessor(documents_dir)
        self.criteria_extractor = CriteriaExtractor(model_name)
        self.bidder_analyzer = BidderAnalyzer(model_name)
        self.scoring_engine = ScoringEngine(model_name, minimum_passing_score)
        
        # Results storage
        self.processed_documents = None
        self.extracted_criteria = None
        self.analysis_results = None
        self.scorecards = None
        
        logger.info(f"RFP Scoring System initialized with model: {model_name}")

    def process_documents(self, force_reprocess: bool = False) -> Dict[str, List[ProcessedDocument]]:
        """Process all documents in the documents directory"""
        
        processed_file = "processed_documents.json"
        
        if not force_reprocess and os.path.exists(processed_file):
            logger.info("Loading existing processed documents...")
            with open(processed_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Convert back to ProcessedDocument objects
            processed_docs = {'rfp': [], 'bidder': []}
            for doc_type in ['rfp', 'bidder']:
                for doc_data in data.get(doc_type, []):
                    processed_docs[doc_type].append(ProcessedDocument(**doc_data))
            
            self.processed_documents = processed_docs
            return processed_docs
        
        logger.info("Processing documents...")
        processed_docs = self.document_processor.process_all_documents()
        self.document_processor.save_processed_documents(processed_docs, processed_file)
        self.processed_documents = processed_docs
        
        return processed_docs

    def extract_criteria(self, force_reextract: bool = False) -> List[EvaluationCriteria]:
        """Extract evaluation criteria from RFP documents"""
        
        criteria_file = "extracted_criteria.json"
        existing_criteria_file = "combined_table.txt"
        
        # Try to load existing criteria first
        if os.path.exists(existing_criteria_file) and not force_reextract:
            logger.info("Loading existing criteria from combined_table.txt...")
            criteria_list = self.criteria_extractor.load_existing_criteria(existing_criteria_file)
            if criteria_list:
                self.extracted_criteria = criteria_list
                return criteria_list
        
        # Extract from RFP documents
        if not self.processed_documents:
            self.process_documents()
        
        if not self.processed_documents['rfp']:
            logger.warning("No RFP documents found for criteria extraction")
            return []
        
        logger.info("Extracting criteria from RFP documents...")
        criteria_data = self.criteria_extractor.process_multiple_rfp_documents(
            self.processed_documents['rfp']
        )
        self.criteria_extractor.save_extracted_criteria(criteria_data, criteria_file)
        
        # Convert to EvaluationCriteria objects
        criteria_list = []
        for doc_name, doc_criteria in criteria_data.items():
            for criteria_item in doc_criteria.get('table_criteria', []):
                criteria_list.append(EvaluationCriteria(**criteria_item))
        
        self.extracted_criteria = criteria_list
        return criteria_list

    def analyze_bidders(self, force_reanalyze: bool = False) -> Dict[str, Any]:
        """Analyze bidder documents against criteria"""
        
        analysis_file = "bidder_analysis_results.json"
        
        if not force_reanalyze and os.path.exists(analysis_file):
            logger.info("Loading existing bidder analysis...")
            with open(analysis_file, 'r', encoding='utf-8') as f:
                self.analysis_results = json.load(f)
            return self.analysis_results
        
        if not self.processed_documents:
            self.process_documents()
        
        if not self.extracted_criteria:
            self.extract_criteria()
        
        if not self.processed_documents['bidder']:
            logger.warning("No bidder documents found for analysis")
            return {}
        
        logger.info("Analyzing bidder documents...")
        with tqdm(total=len(self.processed_documents['bidder']), desc="Analyzing bidders") as pbar:
            analysis_results = self.bidder_analyzer.analyze_multiple_bidders(
                self.processed_documents['bidder'], 
                self.extracted_criteria
            )
            pbar.update(len(self.processed_documents['bidder']))
        
        self.bidder_analyzer.save_analysis_results(analysis_results, analysis_file)
        self.analysis_results = analysis_results
        
        return analysis_results

    def score_bidders(self, force_rescore: bool = False) -> Dict[str, BidderScorecard]:
        """Score all bidders and create scorecards"""
        
        scorecards_file = "bidder_scorecards.json"
        
        if not force_rescore and os.path.exists(scorecards_file):
            logger.info("Loading existing scorecards...")
            with open(scorecards_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Convert back to BidderScorecard objects
            scorecards = {}
            for name, scorecard_data in data.items():
                scorecards[name] = BidderScorecard(**scorecard_data)
            
            self.scorecards = scorecards
            return scorecards
        
        if not self.analysis_results:
            self.analyze_bidders()
        
        if not self.extracted_criteria:
            self.extract_criteria()
        
        logger.info("Scoring bidders...")
        with tqdm(total=len(self.analysis_results.get('bidder_analyses', {})), desc="Scoring bidders") as pbar:
            scorecards = self.scoring_engine.score_all_bidders(
                self.analysis_results, 
                self.extracted_criteria
            )
            pbar.update(len(self.analysis_results.get('bidder_analyses', {})))
        
        self.scoring_engine.save_scorecards(scorecards, scorecards_file)
        self.scorecards = scorecards
        
        return scorecards

    def generate_reports(self, output_dir: str = "reports") -> Dict[str, str]:
        """Generate comprehensive reports"""
        
        if not self.scorecards:
            self.score_bidders()
        
        # Create reports directory
        Path(output_dir).mkdir(exist_ok=True)
        
        reports = {}
        
        # 1. Summary Report
        summary = self.scoring_engine.generate_summary_report(self.scorecards)
        summary_file = os.path.join(output_dir, "evaluation_summary.json")
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        reports['summary'] = summary_file
        
        # 2. Detailed Scorecards (CSV)
        scorecard_data = []
        for bidder_name, scorecard in self.scorecards.items():
            for score in scorecard.individual_scores:
                scorecard_data.append({
                    'Bidder Name': bidder_name,
                    'Criteria ID': score.criteria_id,
                    'Criteria Text': score.criteria_text[:100] + '...',
                    'Awarded Marks': score.awarded_marks,
                    'Max Marks': score.max_marks,
                    'Percentage': (score.awarded_marks / score.max_marks * 100) if score.max_marks > 0 else 0,
                    'Confidence Score': score.confidence_score,
                    'Manual Review Required': score.manual_review_required
                })
        
        csv_file = os.path.join(output_dir, "detailed_scores.csv")
        pd.DataFrame(scorecard_data).to_csv(csv_file, index=False)
        reports['detailed_csv'] = csv_file
        
        # 3. Bidder Rankings
        rankings = []
        for bidder_name, scorecard in self.scorecards.items():
            rankings.append({
                'Bidder Name': bidder_name,
                'Total Score': scorecard.total_score,
                'Max Possible Score': scorecard.max_possible_score,
                'Percentage Score': scorecard.percentage_score,
                'Qualification Status': scorecard.qualification_status,
                'Document Filename': scorecard.document_filename
            })
        
        rankings_df = pd.DataFrame(rankings).sort_values('Percentage Score', ascending=False)
        rankings_file = os.path.join(output_dir, "bidder_rankings.csv")
        rankings_df.to_csv(rankings_file, index=False)
        reports['rankings'] = rankings_file
        
        logger.info(f"Reports generated in: {output_dir}")
        return reports

    def run_complete_evaluation(self, force_reprocess: bool = False) -> Dict[str, Any]:
        """Run the complete evaluation process"""
        
        logger.info("Starting complete RFP evaluation process...")
        
        # Step 1: Process documents
        logger.info("Step 1: Processing documents...")
        processed_docs = self.process_documents(force_reprocess)
        
        # Step 2: Extract criteria
        logger.info("Step 2: Extracting evaluation criteria...")
        criteria = self.extract_criteria(force_reprocess)
        
        # Step 3: Analyze bidders
        logger.info("Step 3: Analyzing bidder documents...")
        analysis_results = self.analyze_bidders(force_reprocess)
        
        # Step 4: Score bidders
        logger.info("Step 4: Scoring bidders...")
        scorecards = self.score_bidders(force_reprocess)
        
        # Step 5: Generate reports
        logger.info("Step 5: Generating reports...")
        reports = self.generate_reports()
        
        # Summary
        summary = {
            'processed_documents': {
                'rfp_count': len(processed_docs['rfp']),
                'bidder_count': len(processed_docs['bidder'])
            },
            'criteria_count': len(criteria),
            'analysis_results': {
                'bidders_analyzed': len(analysis_results.get('bidder_analyses', {}))
            },
            'scoring_results': {
                'total_bidders': len(scorecards),
                'qualified_bidders': len([s for s in scorecards.values() if s.qualification_status == 'QUALIFIED']),
                'pending_review': len([s for s in scorecards.values() if s.qualification_status == 'PENDING_REVIEW']),
                'not_qualified': len([s for s in scorecards.values() if s.qualification_status == 'NOT_QUALIFIED'])
            },
            'reports_generated': reports,
            'evaluation_timestamp': datetime.now().isoformat()
        }
        
        logger.info("Complete evaluation process finished successfully!")
        return summary

    def print_summary(self):
        """Print evaluation summary to console"""
        if not self.scorecards:
            print("No scoring results available. Run evaluation first.")
            return
        
        print("\n" + "="*60)
        print("RFP BIDDER EVALUATION SUMMARY")
        print("="*60)
        
        summary = self.scoring_engine.generate_summary_report(self.scorecards)
        eval_summary = summary['evaluation_summary']
        
        print(f"Total Bidders Evaluated: {eval_summary['total_bidders']}")
        print(f"Minimum Passing Score: {eval_summary['minimum_passing_score']}%")
        print(f"Average Score: {eval_summary['average_score']}%")
        print()
        
        print("QUALIFICATION STATUS:")
        print(f"  ✓ Qualified: {eval_summary['qualified_bidders']}")
        print(f"  ⚠ Pending Review: {eval_summary['pending_review']}")
        print(f"  ✗ Not Qualified: {eval_summary['not_qualified']}")
        print()
        
        print("BIDDER RANKINGS:")
        for i, (bidder, score) in enumerate(summary['bidder_rankings'][:5], 1):
            status = self.scorecards[bidder].qualification_status
            status_symbol = "✓" if status == "QUALIFIED" else "⚠" if status == "PENDING_REVIEW" else "✗"
            print(f"  {i}. {bidder}: {score:.1f}% {status_symbol}")
        
        print("="*60)

def main():
    """Main function for command-line usage"""
    parser = argparse.ArgumentParser(description="RFP Bidder Scoring System")
    parser.add_argument("--documents-dir", default="documents", help="Directory containing RFP and bidder documents")
    parser.add_argument("--model", default="llama3.2:3b", help="LLM model to use")
    parser.add_argument("--min-score", type=float, default=70.0, help="Minimum passing score")
    parser.add_argument("--force-reprocess", action="store_true", help="Force reprocessing of all steps")
    parser.add_argument("--reports-dir", default="reports", help="Directory for generated reports")
    
    args = parser.parse_args()
    
    # Initialize system
    system = RFPScoringSystem(
        documents_dir=args.documents_dir,
        model_name=args.model,
        minimum_passing_score=args.min_score
    )
    
    # Run evaluation
    try:
        summary = system.run_complete_evaluation(args.force_reprocess)
        system.print_summary()
        
        print(f"\nDetailed results saved to:")
        for report_type, file_path in summary['reports_generated'].items():
            print(f"  {report_type}: {file_path}")
            
    except Exception as e:
        logger.error(f"Evaluation failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
