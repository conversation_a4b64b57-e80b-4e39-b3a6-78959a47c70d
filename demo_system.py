#!/usr/bin/env python3
"""
Demo script showing the RFP scoring system functionality
This demonstrates the enhanced system compared to the original rfpdemo.py
"""

import json
import os
from document_processor import DocumentProcessor
from criteria_extractor import CriteriaExtractor
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_ollama import ChatOllama
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser

def demo_document_processing():
    """Demo document processing capabilities"""
    print("=" * 60)
    print("DOCUMENT PROCESSING DEMO")
    print("=" * 60)
    
    processor = DocumentProcessor()
    processed_docs = processor.process_all_documents()
    
    print(f"📄 Processed Documents:")
    print(f"   RFP Documents: {len(processed_docs['rfp'])}")
    for doc in processed_docs['rfp']:
        print(f"     • {doc.filename} ({doc.metadata['word_count']} words)")
    
    print(f"   Bidder Documents: {len(processed_docs['bidder'])}")
    for doc in processed_docs['bidder']:
        print(f"     • {doc.filename} ({doc.metadata['word_count']} words)")
    
    return processed_docs

def demo_criteria_extraction():
    """Demo criteria extraction"""
    print("\n" + "=" * 60)
    print("CRITERIA EXTRACTION DEMO")
    print("=" * 60)
    
    extractor = CriteriaExtractor()
    criteria_list = extractor.load_existing_criteria()
    
    print(f"📋 Evaluation Criteria: {len(criteria_list)} criteria loaded")
    
    for i, criteria in enumerate(criteria_list, 1):
        if criteria.criteria_text.strip():
            print(f"\n{i}. {criteria.criteria_text[:80]}...")
            print(f"   Max Marks: {criteria.max_marks}")
            if criteria.marking_system:
                print(f"   Marking: {criteria.marking_system}")
    
    return criteria_list

def demo_enhanced_rag_system(processed_docs, criteria_list):
    """Demo enhanced RAG system with multiple documents"""
    print("\n" + "=" * 60)
    print("ENHANCED RAG SYSTEM DEMO")
    print("=" * 60)
    
    # Initialize components
    llm = ChatOllama(model="llama3.2:3b")
    embeddings = HuggingFaceEmbeddings(model_name="sentence-transformers/all-MiniLM-L6-v2")
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=600, chunk_overlap=100)
    
    # Create combined knowledge base from all documents
    all_content = []
    
    # Add RFP content
    for doc in processed_docs['rfp']:
        all_content.append(f"RFP Document ({doc.filename}):\n{doc.content}")
    
    # Add bidder content
    for doc in processed_docs['bidder']:
        all_content.append(f"Bidder Document ({doc.filename}):\n{doc.content}")
    
    # Add criteria content
    criteria_content = json.dumps([c.__dict__ for c in criteria_list], indent=2)
    all_content.append(f"Evaluation Criteria:\n{criteria_content}")
    
    # Create vector store
    print("🔍 Creating enhanced vector store...")
    combined_content = "\n\n".join(all_content)
    chunks = text_splitter.create_documents([combined_content])
    vector_store = FAISS.from_documents(chunks, embeddings)
    retriever = vector_store.as_retriever(search_type="similarity", search_kwargs={"k": 5})
    
    # Enhanced prompt template
    enhanced_prompt = PromptTemplate.from_template("""
    You are an expert RFP evaluation assistant with access to comprehensive information about:
    - RFP documents and evaluation criteria
    - Bidder proposals and qualifications
    - Scoring systems and marking schemes
    
    Context Information:
    {context}
    
    Question: {question}
    
    Please provide a detailed analysis including:
    1. Relevant information found in the documents
    2. Specific scoring based on the marking system
    3. Justification for the score
    4. Any missing information or concerns
    
    Answer:
    """)
    
    # Create enhanced RAG chain
    rag_chain = enhanced_prompt | llm | StrOutputParser()
    
    # Demo queries
    demo_queries = [
        "If the bidder is a firm registered in India through competent agency/authority for 10 years how much marks will be provided?",
        "What is the experience of the bidder in government projects and how should it be scored?",
        "Analyze the bidder's financial turnover and assign appropriate marks",
        "What are the key personnel qualifications mentioned by the bidder?"
    ]
    
    print("🤖 Running enhanced queries...")
    
    for i, query in enumerate(demo_queries, 1):
        print(f"\n📝 Query {i}: {query}")
        print("-" * 50)
        
        try:
            # Retrieve relevant context
            retrieved_docs = retriever.invoke(query)
            context = "\n\n".join([doc.page_content for doc in retrieved_docs])
            
            # Get response
            answer = rag_chain.invoke({
                "context": context,
                "question": query
            })
            
            print(f"💡 Answer: {answer[:300]}...")
            
        except Exception as e:
            print(f"❌ Error processing query: {str(e)}")

def demo_scoring_comparison():
    """Demo scoring comparison between original and enhanced system"""
    print("\n" + "=" * 60)
    print("SCORING COMPARISON DEMO")
    print("=" * 60)
    
    # Original system (from rfpdemo.py)
    print("📊 Original System Results:")
    if os.path.exists("combined_table.txt"):
        # Simulate original system
        print("   • Single document processing")
        print("   • Basic RAG with fixed criteria")
        print("   • Manual query processing")
        print("   • Limited context understanding")
    
    print("\n🚀 Enhanced System Capabilities:")
    print("   • Multi-document processing (RFP + Bidder docs)")
    print("   • Automated criteria extraction")
    print("   • Intelligent bidder analysis")
    print("   • Automated scoring with confidence levels")
    print("   • Batch processing with progress tracking")
    print("   • Comprehensive reporting and export")
    print("   • Configurable for different RFP types")
    print("   • Built-in testing and validation")

def main():
    """Main demo function"""
    print("🎯 RFP BIDDER SCORING SYSTEM - COMPREHENSIVE DEMO")
    print("Using Open Source Models: Llama 3.2 + Sentence Transformers")
    
    try:
        # Demo 1: Document Processing
        processed_docs = demo_document_processing()
        
        # Demo 2: Criteria Extraction
        criteria_list = demo_criteria_extraction()
        
        # Demo 3: Enhanced RAG System
        demo_enhanced_rag_system(processed_docs, criteria_list)
        
        # Demo 4: Scoring Comparison
        demo_scoring_comparison()
        
        print("\n" + "=" * 60)
        print("✅ DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        print("\n🔧 Next Steps:")
        print("1. Run full evaluation: python rfp_scoring_system.py")
        print("2. Check generated reports in the 'reports' folder")
        print("3. Customize configuration in config.py")
        print("4. Add more RFP and bidder documents to 'documents' folder")
        print("5. Run tests: python test_system.py")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {str(e)}")
        print("Please ensure:")
        print("- Ollama is running (ollama serve)")
        print("- Required models are available (ollama list)")
        print("- All dependencies are installed (pip install -r requirements.txt)")

if __name__ == "__main__":
    main()
