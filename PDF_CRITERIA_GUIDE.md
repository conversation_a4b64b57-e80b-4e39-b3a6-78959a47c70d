# 📄 PDF Criteria Extraction Guide

## 🎯 Overview

The RFP Scoring System now supports **PDF criteria files** in addition to JSON and Excel formats. The system uses multiple extraction methods to handle different PDF layouts and structures.

## 🔧 How PDF Criteria Extraction Works

### **Three-Tier Extraction Strategy**

```mermaid
flowchart TD
    A[📄 PDF Criteria File] --> B[🔍 Method 1: Table Extraction]
    B --> C{Tables Found?}
    C -->|Yes| D[✅ Extract from Tables]
    C -->|No| E[🤖 Method 2: LLM Text Analysis]
    E --> F{LLM Success?}
    F -->|Yes| G[✅ Extract with LLM]
    F -->|No| H[📝 Method 3: Simple Text Parsing]
    H --> I[✅ Rule-based Extraction]
    
    D --> J[📋 Structured Criteria]
    G --> J
    I --> J
```

### **Method 1: Table Extraction (Most Accurate)**

**Best for**: PDFs with structured tables containing evaluation criteria

**How it works**:
- Uses `pdfplumber` to detect and extract tables
- Identifies criteria tables by looking for headers containing: "criteria", "evaluation", "requirement", "marks"
- Maps table columns to criteria fields automatically
- Extracts serial numbers, criteria text, marking systems, and max marks

**Example PDF Table Structure**:
```
| S.No | Evaluation Criteria | Marking System | Max Marks |
|------|-------------------|----------------|-----------|
| 1    | Firm registered for 10+ years | 10-15 yrs = 10, >15 yrs = 20 | 20 |
| 2    | Financial turnover Rs. 50 lac | Rs. 50-100 lac = 05 | 10 |
```

### **Method 2: LLM Text Analysis (Most Flexible)**

**Best for**: PDFs with unstructured text containing criteria descriptions

**How it works**:
- Extracts all text from PDF using `pdfplumber`
- Uses Ollama LLM to analyze text and identify evaluation criteria
- Prompts LLM to structure findings as JSON
- Handles complex document layouts and varied formatting

**Example Text Input**:
```
The bidder firm should be registered in India through competent 
agency/authority for at least 10 years. Marking: 10-15 years = 10 marks, 
more than 15 years = 20 marks.

The firm should have minimum average annual financial turnover of 
Rs. 50 lac during the last 3 completed financial years.
```

### **Method 3: Simple Text Parsing (Most Reliable)**

**Best for**: Fallback when other methods fail

**How it works**:
- Uses regex patterns to identify criteria sentences
- Looks for keywords: "should", "must", "firm", "experience", "qualification"
- Searches for marking systems with patterns: "=", "marks", "yrs", "lac"
- Creates basic criteria structure from found patterns

## 📋 Supported PDF Formats

### ✅ **Well-Supported Formats**

1. **Tabular PDFs**
   - Clear table structures with headers
   - Consistent column layouts
   - Readable text (not scanned images)

2. **Structured Text PDFs**
   - Numbered criteria lists
   - Clear marking systems
   - Consistent formatting

3. **RFP Documents**
   - Technical evaluation sections
   - Scoring rubrics
   - Qualification requirements

### ⚠️ **Challenging Formats**

1. **Scanned PDFs** (Image-based)
   - Requires OCR preprocessing
   - May have text recognition errors
   - Consider converting to text-based PDF first

2. **Complex Layouts**
   - Multi-column formats
   - Mixed content types
   - Inconsistent formatting

3. **Non-English Content**
   - Currently optimized for English
   - May work with other languages but not tested

## 🚀 Usage Examples

### **Example 1: Upload PDF Criteria**

1. **Prepare PDF**: Ensure your RFP PDF contains evaluation criteria
2. **Upload**: Select PDF file in the criteria upload section
3. **Process**: System automatically detects best extraction method
4. **Review**: Check extracted criteria in the results

### **Example 2: Mixed Format Support**

```python
# The system supports multiple formats simultaneously
supported_formats = [
    "evaluation_criteria.json",    # JSON format
    "criteria_table.xlsx",         # Excel format  
    "rfp_document.pdf"            # PDF format (NEW!)
]
```

### **Example 3: Extraction Results**

**Input PDF Content**:
```
1. The firm should be registered in India for at least 10 years
   Marking: 10-15 years = 10 marks, >15 years = 20 marks

2. Minimum financial turnover of Rs. 50 lac
   Marking: Rs. 50-100 lac = 5 marks, >Rs. 100 lac = 10 marks
```

**Extracted JSON Structure**:
```json
[
  {
    "serial_number": "1",
    "criteria_text": "The firm should be registered in India for at least 10 years",
    "marking_system": "10-15 years = 10 marks, >15 years = 20 marks",
    "max_marks": "20"
  },
  {
    "serial_number": "2", 
    "criteria_text": "Minimum financial turnover of Rs. 50 lac",
    "marking_system": "Rs. 50-100 lac = 5 marks, >Rs. 100 lac = 10 marks",
    "max_marks": "10"
  }
]
```

## 🔍 Troubleshooting

### **Common Issues and Solutions**

#### **Issue**: No criteria extracted from PDF
**Solutions**:
- Check if PDF contains readable text (not just images)
- Ensure criteria are clearly formatted with numbers or bullets
- Try converting scanned PDF to text-based PDF
- Use JSON or Excel format as alternative

#### **Issue**: Incorrect criteria extraction
**Solutions**:
- Review PDF structure - tables work better than free text
- Check for clear marking systems (numbers, "=", "marks")
- Manually verify extracted criteria before processing
- Consider reformatting PDF for better structure

#### **Issue**: Missing marking systems
**Solutions**:
- Ensure marking systems are on same page as criteria
- Use consistent format: "X-Y = Z marks"
- Place marking info close to criteria text
- Consider adding marking systems manually in JSON format

### **Best Practices for PDF Criteria**

1. **Structure Your PDF**:
   - Use tables when possible
   - Number criteria clearly (1, 2, 3...)
   - Keep marking systems close to criteria

2. **Clear Formatting**:
   - Use consistent fonts and sizes
   - Avoid complex layouts
   - Separate criteria clearly

3. **Marking Systems**:
   - Use standard formats: "10-15 yrs = 10"
   - Include "marks" or "points" keywords
   - Be specific about ranges and conditions

4. **Testing**:
   - Test extraction with your PDF before important evaluations
   - Review extracted criteria for accuracy
   - Have JSON/Excel backup if needed

## 📊 Performance Comparison

| Method | Speed | Accuracy | Flexibility |
|--------|-------|----------|-------------|
| **Table Extraction** | ⚡ Fast | 🎯 95%+ | 📋 Structured PDFs |
| **LLM Analysis** | 🐌 Slow | 🎯 80-90% | 🔄 Any format |
| **Text Parsing** | ⚡ Very Fast | 🎯 70-80% | 📝 Simple patterns |

## 🎯 Integration with Scoring System

The PDF criteria extraction integrates seamlessly with the existing scoring system:

1. **Upload PDF** → System detects format automatically
2. **Extract Criteria** → Uses best available method
3. **Score Bidders** → Same rule-based + LLM scoring
4. **Generate Reports** → Standard output formats

## 🔧 Technical Implementation

### **Key Components**

- `extract_criteria_from_pdf()` - Main extraction coordinator
- `extract_criteria_from_pdf_tables()` - Table-based extraction
- `extract_criteria_from_pdf_text()` - LLM-based extraction  
- `simple_text_criteria_extraction()` - Rule-based fallback

### **Dependencies**

- `pdfplumber` - PDF text and table extraction
- `langchain_ollama` - LLM text analysis
- `re` - Regular expression pattern matching
- `json` - JSON parsing and generation

## 🚀 Future Enhancements

- **OCR Support**: Handle scanned/image-based PDFs
- **Multi-language**: Support for non-English criteria
- **Advanced Layouts**: Better handling of complex PDF structures
- **Confidence Scoring**: Rate extraction quality automatically
- **Interactive Review**: Allow manual correction of extracted criteria

The PDF criteria extraction makes the system more flexible and user-friendly, allowing you to work directly with your existing RFP documents without manual conversion! 📄✨
