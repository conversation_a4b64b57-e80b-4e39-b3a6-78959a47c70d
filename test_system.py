"""
Test Suite for RFP Scoring System

This module provides comprehensive testing for all components of the RFP scoring system,
including unit tests, integration tests, and validation with provided documents.
"""

import unittest
import os
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Import system components
from document_processor import DocumentProcessor, ProcessedDocument
from criteria_extractor import CriteriaExtractor, EvaluationCriteria
from bidder_analyzer import BidderAnalyzer, BidderAnalysis
from scoring_engine import ScoringEngine, ScoringResult, BidderScorecard
from rfp_scoring_system import RFPScoringSystem
from config import ConfigManager

class TestDocumentProcessor(unittest.TestCase):
    """Test document processing functionality"""
    
    def setUp(self):
        self.test_dir = tempfile.mkdtemp()
        self.processor = DocumentProcessor(self.test_dir)
    
    def tearDown(self):
        shutil.rmtree(self.test_dir)
    
    def test_extract_text_from_txt(self):
        """Test text extraction from TXT files"""
        test_file = Path(self.test_dir) / "test.txt"
        test_content = "This is a test document content."
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        extracted_text = self.processor.extract_text_from_txt(test_file)
        self.assertEqual(extracted_text, test_content)
    
    def test_classify_document_type(self):
        """Test document type classification"""
        # Test RFP classification
        rfp_content = "This is an RFP document with evaluation criteria and marking system."
        doc_type = self.processor.classify_document_type("rfp_document.pdf", rfp_content)
        self.assertEqual(doc_type, 'rfp')
        
        # Test bidder classification
        bidder_content = "This is a bidder proposal with company profile and experience."
        doc_type = self.processor.classify_document_type("bidder_proposal.pdf", bidder_content)
        self.assertEqual(doc_type, 'bidder')
    
    def test_process_single_document(self):
        """Test processing of a single document"""
        test_file = Path(self.test_dir) / "test_rfp.txt"
        test_content = "RFP document with evaluation criteria and technical requirements."
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        processed_doc = self.processor.process_single_document(test_file)
        
        self.assertIsNotNone(processed_doc)
        self.assertEqual(processed_doc.filename, "test_rfp.txt")
        self.assertEqual(processed_doc.document_type, "rfp")
        self.assertEqual(processed_doc.content, test_content)
        self.assertIn('word_count', processed_doc.metadata)

class TestCriteriaExtractor(unittest.TestCase):
    """Test criteria extraction functionality"""
    
    def setUp(self):
        self.extractor = CriteriaExtractor()
    
    def test_load_existing_criteria(self):
        """Test loading existing criteria from combined_table.txt"""
        if os.path.exists("combined_table.txt"):
            criteria_list = self.extractor.load_existing_criteria()
            self.assertIsInstance(criteria_list, list)
            if criteria_list:
                self.assertIsInstance(criteria_list[0], EvaluationCriteria)
    
    @patch('criteria_extractor.ChatOllama')
    def test_extract_criteria_from_text(self, mock_llm):
        """Test criteria extraction from text using mocked LLM"""
        # Mock LLM response
        mock_response = json.dumps({
            "evaluation_criteria": [
                {
                    "serial_number": "1",
                    "criteria_text": "Test criteria",
                    "relevant_document": "Test document",
                    "marking_system": "Test marking",
                    "max_marks": "10"
                }
            ]
        })
        
        mock_llm.return_value.invoke.return_value = mock_response
        
        result = self.extractor.extract_criteria_from_text("Test RFP content")
        self.assertIn('evaluation_criteria', result)
        self.assertEqual(len(result['evaluation_criteria']), 1)

class TestBidderAnalyzer(unittest.TestCase):
    """Test bidder analysis functionality"""
    
    def setUp(self):
        self.analyzer = BidderAnalyzer()
    
    def test_extract_bidder_name(self):
        """Test bidder name extraction from filename"""
        filename = "Dummy Bidder Document1.pdf"
        bidder_name = self.analyzer.extract_bidder_name(filename)
        self.assertEqual(bidder_name, "Dummy 1")
    
    def test_create_vector_store(self):
        """Test vector store creation"""
        test_content = "This is test content for vector store creation."
        vector_store = self.analyzer.create_vector_store(test_content)
        self.assertIsNotNone(vector_store)
    
    def test_extract_relevant_sections(self):
        """Test relevant section extraction using similarity search"""
        document_content = """
        Company ABC has 15 years of experience in audit services.
        We have completed 8 government projects in the last 3 years.
        Our team consists of qualified chartered accountants.
        """
        
        criteria_text = "Experience in audit services"
        relevant_sections = self.analyzer.extract_relevant_sections(document_content, criteria_text)
        
        self.assertIsInstance(relevant_sections, list)
        self.assertTrue(len(relevant_sections) > 0)

class TestScoringEngine(unittest.TestCase):
    """Test scoring engine functionality"""
    
    def setUp(self):
        self.scoring_engine = ScoringEngine()
    
    def test_extract_numeric_value(self):
        """Test numeric value extraction from text"""
        # Test years extraction
        text1 = "Company has 15 years of experience"
        value1 = self.scoring_engine.extract_numeric_value(text1)
        self.assertEqual(value1, 15.0)
        
        # Test amount extraction
        text2 = "Turnover of Rs. 100 lac"
        value2 = self.scoring_engine.extract_numeric_value(text2)
        self.assertEqual(value2, 100.0)
        
        # Test project count extraction
        text3 = "Completed 8 projects"
        value3 = self.scoring_engine.extract_numeric_value(text3)
        self.assertEqual(value3, 8.0)
    
    def test_apply_scoring_rules(self):
        """Test application of scoring rules"""
        scoring_rules = {
            "scoring_rules": [
                {
                    "condition": "10-15 years",
                    "marks": "10",
                    "range_min": "10",
                    "range_max": "15"
                },
                {
                    "condition": ">15 years",
                    "marks": "20",
                    "range_min": "15",
                    "range_max": None
                }
            ],
            "default_marks": "0",
            "evaluation_type": "range"
        }
        
        # Test scoring for 12 years (should get 10 marks)
        extracted_info = "Company has 12 years of experience"
        marks, rationale = self.scoring_engine.apply_scoring_rules(extracted_info, scoring_rules, "20")
        self.assertEqual(marks, 10.0)
        
        # Test scoring for 18 years (should get 20 marks)
        extracted_info = "Company has 18 years of experience"
        marks, rationale = self.scoring_engine.apply_scoring_rules(extracted_info, scoring_rules, "20")
        self.assertEqual(marks, 20.0)
    
    def test_create_bidder_scorecard(self):
        """Test bidder scorecard creation"""
        scoring_results = [
            ScoringResult(
                bidder_name="Test Bidder",
                criteria_id="1",
                criteria_text="Test criteria",
                extracted_info="Test info",
                awarded_marks=15.0,
                max_marks=20.0,
                scoring_rationale="Test rationale",
                confidence_score=0.8,
                manual_review_required=False
            ),
            ScoringResult(
                bidder_name="Test Bidder",
                criteria_id="2",
                criteria_text="Test criteria 2",
                extracted_info="Test info 2",
                awarded_marks=8.0,
                max_marks=10.0,
                scoring_rationale="Test rationale 2",
                confidence_score=0.9,
                manual_review_required=False
            )
        ]
        
        scorecard = self.scoring_engine.create_bidder_scorecard(
            "Test Bidder", "test_document.pdf", scoring_results
        )
        
        self.assertEqual(scorecard.bidder_name, "Test Bidder")
        self.assertEqual(scorecard.total_score, 23.0)
        self.assertEqual(scorecard.max_possible_score, 30.0)
        self.assertAlmostEqual(scorecard.percentage_score, 76.67, places=1)
        self.assertEqual(scorecard.qualification_status, "QUALIFIED")

class TestRFPScoringSystem(unittest.TestCase):
    """Test the complete RFP scoring system"""
    
    def setUp(self):
        self.test_dir = tempfile.mkdtemp()
        self.system = RFPScoringSystem(documents_dir=self.test_dir)
    
    def tearDown(self):
        shutil.rmtree(self.test_dir)
    
    def test_system_initialization(self):
        """Test system initialization"""
        self.assertIsNotNone(self.system.document_processor)
        self.assertIsNotNone(self.system.criteria_extractor)
        self.assertIsNotNone(self.system.bidder_analyzer)
        self.assertIsNotNone(self.system.scoring_engine)
    
    def test_process_documents_empty_directory(self):
        """Test document processing with empty directory"""
        processed_docs = self.system.process_documents()
        self.assertEqual(len(processed_docs['rfp']), 0)
        self.assertEqual(len(processed_docs['bidder']), 0)

class TestConfigManager(unittest.TestCase):
    """Test configuration management"""
    
    def setUp(self):
        self.test_config_file = "test_config.json"
        self.config_manager = ConfigManager(self.test_config_file)
    
    def tearDown(self):
        if os.path.exists(self.test_config_file):
            os.remove(self.test_config_file)
    
    def test_default_config_creation(self):
        """Test default configuration creation"""
        config = self.config_manager.get_default_config()
        self.assertIn('system', config)
        self.assertIn('models', config)
        self.assertIn('scoring', config)
        self.assertIn('processing', config)
    
    def test_config_get_set(self):
        """Test configuration get and set operations"""
        # Test setting a value
        self.config_manager.set('test.value', 'test_data')
        
        # Test getting the value
        value = self.config_manager.get('test.value')
        self.assertEqual(value, 'test_data')
        
        # Test getting non-existent value with default
        value = self.config_manager.get('non.existent', 'default')
        self.assertEqual(value, 'default')
    
    def test_model_config(self):
        """Test model configuration retrieval"""
        model_config = self.config_manager.get_model_config()
        self.assertIsNotNone(model_config.name)
        self.assertIsInstance(model_config.temperature, float)
    
    def test_config_validation(self):
        """Test configuration validation"""
        issues = self.config_manager.validate_config()
        # Should have minimal issues with default config
        self.assertIsInstance(issues, list)

class IntegrationTest(unittest.TestCase):
    """Integration tests with actual documents"""
    
    def setUp(self):
        self.documents_dir = "documents"
        if not os.path.exists(self.documents_dir):
            self.skipTest("Documents directory not found")
    
    def test_end_to_end_processing(self):
        """Test end-to-end processing with actual documents"""
        system = RFPScoringSystem(documents_dir=self.documents_dir)
        
        # Process documents
        processed_docs = system.process_documents()
        self.assertIsInstance(processed_docs, dict)
        self.assertIn('rfp', processed_docs)
        self.assertIn('bidder', processed_docs)
        
        # If we have documents, test further processing
        if processed_docs['rfp'] or processed_docs['bidder']:
            # Extract criteria
            criteria = system.extract_criteria()
            self.assertIsInstance(criteria, list)
            
            # If we have both RFP and bidder documents, test analysis
            if processed_docs['rfp'] and processed_docs['bidder']:
                analysis_results = system.analyze_bidders()
                self.assertIsInstance(analysis_results, dict)
                
                # Test scoring
                scorecards = system.score_bidders()
                self.assertIsInstance(scorecards, dict)

def run_validation_tests():
    """Run validation tests with provided documents"""
    print("Running validation tests with provided documents...")
    
    # Check if required files exist
    required_files = [
        "documents/QCBS 1.pdf",
        "documents/Dummy Bidder Document1.pdf",
        "combined_table.txt"
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"Missing required files: {missing_files}")
        return False
    
    try:
        # Test the complete system
        system = RFPScoringSystem()
        summary = system.run_complete_evaluation()
        
        print("Validation Results:")
        print(f"✓ Processed {summary['processed_documents']['rfp_count']} RFP documents")
        print(f"✓ Processed {summary['processed_documents']['bidder_count']} bidder documents")
        print(f"✓ Extracted {summary['criteria_count']} evaluation criteria")
        print(f"✓ Analyzed {summary['analysis_results']['bidders_analyzed']} bidders")
        print(f"✓ Generated scorecards for {summary['scoring_results']['total_bidders']} bidders")
        
        # Print scoring summary
        system.print_summary()
        
        return True
        
    except Exception as e:
        print(f"Validation failed: {str(e)}")
        return False

if __name__ == "__main__":
    # Run unit tests
    print("Running unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "="*60)
    
    # Run validation tests
    validation_success = run_validation_tests()
    
    if validation_success:
        print("\n✓ All tests passed successfully!")
    else:
        print("\n✗ Some tests failed. Please check the output above.")
