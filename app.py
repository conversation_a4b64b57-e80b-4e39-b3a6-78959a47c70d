import streamlit as st
import json
import pandas as pd
import os
import tempfile
import shutil
from datetime import datetime
from pathlib import Path
from document_processor import DocumentProcessor
from criteria_extractor import CriteriaExtractor
import re
from langchain_ollama import ChatOllama

class SimpleUploadScoringSystem:
    """Simplified scoring system that works with uploaded files only"""
    
    def __init__(self, model_name: str = "llama3.2:3b"):
        self.model_name = model_name
        self.llm = ChatOllama(model=model_name)
        
        # Simple scoring prompt
        self.simple_scoring_prompt = """
        Analyze the bidder information against the evaluation criteria and provide a score.
        
        Criteria: {criteria_text}
        Marking System: {marking_system}
        Maximum Marks: {max_marks}
        
        Bidder Information: {bidder_info}
        
        Based on this information, provide:
        1. Score (number between 0 and {max_marks})
        2. Brief justification (1-2 sentences)
        
        Score: [Your score here]
        Justification: [Your justification here]
        """
    
    def process_uploaded_files(self, bidder_files_dir: str, criteria_file_path: str = None):
        """Process uploaded files and run evaluation"""
        try:
            # Create document processor with uploaded files directory
            doc_processor = DocumentProcessor(bidder_files_dir)
            
            # Process uploaded bidder documents
            processed_docs = doc_processor.process_all_documents()
            
            # Load criteria from uploaded file or fallback to existing
            criteria_list = []
            if criteria_file_path and os.path.exists(criteria_file_path):
                criteria_list = self.load_criteria_from_uploaded_file(criteria_file_path)
            
            # Fallback to existing criteria if no upload or loading failed
            if not criteria_list:
                criteria_extractor = CriteriaExtractor(self.model_name)
                criteria_list = criteria_extractor.load_existing_criteria()
            
            if not criteria_list:
                return {"error": "No evaluation criteria found. Please upload a criteria file or ensure combined_table.txt exists."}
            
            # Run scoring using the robust system logic
            results = self.score_uploaded_bidders(processed_docs['bidder'], criteria_list)
            
            return results
            
        except Exception as e:
            return {"error": f"Processing failed: {str(e)}"}
    
    def load_criteria_from_uploaded_file(self, file_path: str):
        """Load criteria from uploaded file"""
        try:
            if file_path.endswith('.json'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                return self.convert_to_criteria_objects(data)
            elif file_path.endswith('.xlsx'):
                df = pd.read_excel(file_path)
                return self.convert_excel_to_criteria(df)
            else:
                return []
        except Exception as e:
            st.warning(f"Could not load criteria from uploaded file: {str(e)}")
            return []
    
    def convert_to_criteria_objects(self, data):
        """Convert data to EvaluationCriteria objects"""
        from criteria_extractor import EvaluationCriteria
        criteria_list = []
        
        if isinstance(data, list):
            for item in data:
                if isinstance(item, dict):
                    criteria = EvaluationCriteria(
                        serial_number=item.get('S.N.', item.get('serial_number', '')),
                        criteria_text=item.get('Technical Evaluation Criteria', item.get('criteria_text', '')),
                        relevant_document=item.get('Relevant Document', item.get('relevant_document', '')),
                        marking_system=item.get('Marking System', item.get('marking_system', '')),
                        max_marks=item.get('Max.\nMarks', item.get('max_marks', ''))
                    )
                    criteria_list.append(criteria)
        
        return criteria_list
    
    def convert_excel_to_criteria(self, df):
        """Convert Excel DataFrame to criteria objects"""
        from criteria_extractor import EvaluationCriteria
        criteria_list = []
        
        for _, row in df.iterrows():
            criteria = EvaluationCriteria(
                serial_number=str(row.get('S.N.', row.get('Serial Number', ''))),
                criteria_text=str(row.get('Technical Evaluation Criteria', row.get('Criteria', ''))),
                relevant_document=str(row.get('Relevant Document', row.get('Documents', ''))),
                marking_system=str(row.get('Marking System', row.get('Marks', ''))),
                max_marks=str(row.get('Max Marks', row.get('Maximum', '')))
            )
            if criteria.criteria_text.strip():
                criteria_list.append(criteria)
        
        return criteria_list
    
    def parse_marking_system_simple(self, marking_system: str) -> dict:
        """Simple rule-based parsing of marking systems"""
        if not marking_system:
            return {}
        
        rules = {}
        
        # Pattern: "10-15 yrs = 10, >15 yrs = 20"
        range_pattern = r'(\d+)-(\d+)\s*(?:yrs?|years?)\s*=\s*(\d+)'
        greater_pattern = r'>(\d+)\s*(?:yrs?|years?)\s*=\s*(\d+)'
        
        # Find range rules
        for match in re.finditer(range_pattern, marking_system, re.IGNORECASE):
            min_val, max_val, marks = match.groups()
            rules[f"{min_val}-{max_val}"] = float(marks)
        
        # Find greater than rules
        for match in re.finditer(greater_pattern, marking_system, re.IGNORECASE):
            min_val, marks = match.groups()
            rules[f">{min_val}"] = float(marks)
        
        # Pattern: "Rs. 50-100 lac = 05"
        amount_pattern = r'Rs\.?\s*(\d+)-(\d+)\s*(?:lac|lakh)\s*=\s*(\d+)'
        amount_greater_pattern = r'>Rs\.?\s*(\d+)\s*(?:lac|lakh)\s*=\s*(\d+)'
        
        for match in re.finditer(amount_pattern, marking_system, re.IGNORECASE):
            min_val, max_val, marks = match.groups()
            rules[f"Rs.{min_val}-{max_val}lac"] = float(marks)
        
        for match in re.finditer(amount_greater_pattern, marking_system, re.IGNORECASE):
            min_val, marks = match.groups()
            rules[f">Rs.{min_val}lac"] = float(marks)
        
        return rules
    
    def apply_rule_based_scoring(self, bidder_info: str, marking_rules: dict, max_marks: float):
        """Apply rule-based scoring if possible"""
        if not marking_rules:
            return None
        
        # Extract numbers from bidder info
        numbers = [float(x) for x in re.findall(r'\d+(?:\.\d+)?', bidder_info)]
        if not numbers:
            return None
        
        # Try to match against rules
        for rule, marks in marking_rules.items():
            if 'yrs' in rule or 'years' in rule:
                for num in numbers:
                    if '-' in rule:
                        min_val, max_val = rule.split('-')
                        min_val = float(re.findall(r'\d+', min_val)[0])
                        max_val = float(re.findall(r'\d+', max_val)[0])
                        if min_val <= num <= max_val:
                            return min(marks, max_marks), f"Rule-based: {num} years falls in range {min_val}-{max_val}"
                    elif '>' in rule:
                        min_val = float(re.findall(r'\d+', rule)[0])
                        if num > min_val:
                            return min(marks, max_marks), f"Rule-based: {num} years > {min_val}"
            
            elif 'lac' in rule:
                for num in numbers:
                    if '-' in rule:
                        min_val, max_val = [float(x) for x in re.findall(r'\d+', rule)]
                        if min_val <= num <= max_val:
                            return min(marks, max_marks), f"Rule-based: Rs.{num} lac falls in range {min_val}-{max_val}"
                    elif '>' in rule:
                        min_val = float(re.findall(r'\d+', rule)[0])
                        if num > min_val:
                            return min(marks, max_marks), f"Rule-based: Rs.{num} lac > {min_val}"
        
        return None
    
    def extract_score_from_text(self, response: str, max_marks: str):
        """Extract score and justification from text response"""
        try:
            max_marks_value = float(max_marks) if max_marks.isdigit() else 10.0
        except:
            max_marks_value = 10.0
        
        # Extract score
        score_patterns = [
            r'Score:\s*(\d+(?:\.\d+)?)',
            r'score[:\s]*(\d+(?:\.\d+)?)',
            r'marks?[:\s]*(\d+(?:\.\d+)?)',
            r'(\d+(?:\.\d+)?)\s*(?:marks?|points?)',
        ]
        
        awarded_marks = 0.0
        for pattern in score_patterns:
            match = re.search(pattern, response, re.IGNORECASE)
            if match:
                try:
                    awarded_marks = min(float(match.group(1)), max_marks_value)
                    break
                except ValueError:
                    continue
        
        # Extract justification
        justification_patterns = [
            r'Justification:\s*(.+?)(?:\n|$)',
            r'justification[:\s]*(.+?)(?:\n|$)',
            r'rationale[:\s]*(.+?)(?:\n|$)',
        ]
        
        justification = "Score extracted from response"
        for pattern in justification_patterns:
            match = re.search(pattern, response, re.IGNORECASE | re.DOTALL)
            if match:
                justification = match.group(1).strip()[:200]
                break
        
        if not justification or justification == "Score extracted from response":
            justification = response[:200] + "..." if len(response) > 200 else response
        
        return awarded_marks, justification
    
    def score_bidder_against_criteria(self, bidder_info: str, criteria):
        """Score a bidder against a single criteria"""
        
        max_marks = float(criteria.max_marks) if criteria.max_marks.isdigit() else 0.0
        
        # Try rule-based scoring first
        marking_rules = self.parse_marking_system_simple(criteria.marking_system)
        rule_result = self.apply_rule_based_scoring(bidder_info, marking_rules, max_marks)
        
        if rule_result:
            awarded_marks, rationale = rule_result
            return {
                "criteria_id": criteria.serial_number,
                "awarded_marks": awarded_marks,
                "max_marks": max_marks,
                "scoring_method": "rule_based",
                "rationale": rationale,
                "confidence": 0.9
            }
        
        # Fall back to LLM scoring
        try:
            prompt = self.simple_scoring_prompt.format(
                criteria_text=criteria.criteria_text,
                marking_system=criteria.marking_system,
                max_marks=criteria.max_marks,
                bidder_info=bidder_info[:1000]
            )
            
            response = self.llm.invoke(prompt)
            # Handle AIMessage response - FIX FOR THE ERROR
            if hasattr(response, 'content'):
                response_text = response.content
            else:
                response_text = str(response)
            
            awarded_marks, rationale = self.extract_score_from_text(response_text, criteria.max_marks)
            
            return {
                "criteria_id": criteria.serial_number,
                "awarded_marks": awarded_marks,
                "max_marks": max_marks,
                "scoring_method": "llm_based",
                "rationale": rationale,
                "confidence": 0.7
            }
            
        except Exception as e:
            # Ultimate fallback
            return {
                "criteria_id": criteria.serial_number,
                "awarded_marks": 0.0,
                "max_marks": max_marks,
                "scoring_method": "fallback",
                "rationale": f"Scoring failed: {str(e)} - manual review required",
                "confidence": 0.0
            }
    
    def score_uploaded_bidders(self, bidder_docs, criteria_list):
        """Score bidders using uploaded documents"""
        results = {}
        
        for bidder_doc in bidder_docs:
            bidder_name = bidder_doc.filename.replace('.pdf', '').replace('Document', '').strip()
            
            bidder_scores = []
            total_score = 0.0
            max_possible = 0.0
            
            for criteria in criteria_list:
                if criteria.criteria_text.strip():
                    score_result = self.score_bidder_against_criteria(bidder_doc.content, criteria)
                    bidder_scores.append(score_result)
                    total_score += score_result["awarded_marks"]
                    max_possible += score_result["max_marks"]
            
            percentage = (total_score / max_possible * 100) if max_possible > 0 else 0
            
            results[bidder_name] = {
                "individual_scores": bidder_scores,
                "total_score": total_score,
                "max_possible_score": max_possible,
                "percentage_score": percentage,
                "qualification_status": "QUALIFIED" if percentage >= 70 else "NOT_QUALIFIED"
            }
        
        return {
            "results": results,
            "total_bidders": len(results),
            "evaluation_timestamp": datetime.now().isoformat()
        }

# Streamlit UI Setup
st.set_page_config(page_title="RFP Evaluation Suite", layout="wide")
st.title("RFP Evaluation Suite")
st.markdown("Automated scoring of bidder documents based on evaluation criteria.")

# Sidebar file uploads
with st.sidebar:
    st.header("Upload Files")
    bidder_docs = st.file_uploader("Upload Bidder Documents", type=["pdf"], accept_multiple_files=True)
    criteria_file = st.file_uploader("Upload Criteria File (Optional)", type=["json", "xlsx", "pdf"])
    
    start_eval = st.button("Run Evaluation", disabled=len(bidder_docs) == 0)

# Main evaluation logic
if start_eval and bidder_docs:
    with st.spinner("Processing files and running evaluation..."):
        try:
            # Create temporary directories for uploaded files
            upload_base_dir = "temp_uploaded_files"
            bidder_dir = os.path.join(upload_base_dir, "bidder")
            criteria_dir = os.path.join(upload_base_dir, "criteria")
            
            # Clean and create directories
            if os.path.exists(upload_base_dir):
                shutil.rmtree(upload_base_dir)
            os.makedirs(bidder_dir, exist_ok=True)
            os.makedirs(criteria_dir, exist_ok=True)

            # Save uploaded bidder documents
            for file in bidder_docs:
                file_path = os.path.join(bidder_dir, file.name)
                with open(file_path, "wb") as f:
                    f.write(file.getvalue())

            # Save criteria file if uploaded
            criteria_path = None
            if criteria_file is not None:
                criteria_path = os.path.join(criteria_dir, criteria_file.name)
                with open(criteria_path, "wb") as f:
                    f.write(criteria_file.getvalue())

            # Run evaluation with uploaded files only
            scoring_system = SimpleUploadScoringSystem()
            results = scoring_system.process_uploaded_files(bidder_dir, criteria_path)

            # Display results
            if "error" in results:
                st.error(f"Evaluation Failed: {results['error']}")
            else:
                st.success("Evaluation Completed!")
                
                # Summary Table
                summary_data = []
                for bidder_name, bidder_data in results["results"].items():
                    summary_data.append({
                        "Bidder Name": bidder_name,
                        "Total Score": f"{bidder_data['total_score']:.1f}",
                        "Max Score": f"{bidder_data['max_possible_score']:.1f}",
                        "Percentage": f"{bidder_data['percentage_score']:.1f}%",
                        "Status": bidder_data["qualification_status"]
                    })
                
                df_summary = pd.DataFrame(summary_data)
                st.dataframe(df_summary, use_container_width=True)

                # Detailed View per Bidder
                st.subheader("Detailed Scores")
                for bidder_name, bidder_data in results["results"].items():
                    status_icon = "✅" if bidder_data["qualification_status"] == "QUALIFIED" else "❌"
                    with st.expander(f"{status_icon} {bidder_name} - {bidder_data['percentage_score']:.1f}%"):
                        
                        # Individual scores table
                        detailed_data = []
                        for score in bidder_data["individual_scores"]:
                            detailed_data.append({
                                "Criteria ID": score["criteria_id"],
                                "Awarded Marks": f"{score['awarded_marks']:.1f}",
                                "Max Marks": f"{score['max_marks']:.1f}",
                                "Method": score["scoring_method"],
                                "Rationale": score["rationale"][:100] + "..." if len(score["rationale"]) > 100 else score["rationale"]
                            })
                        
                        df_detailed = pd.DataFrame(detailed_data)
                        st.dataframe(df_detailed, use_container_width=True)

                # Download Results
                result_json = json.dumps(results, indent=2)
                st.download_button(
                    label="Download JSON Results",
                    data=result_json,
                    file_name=f"rfp_scoring_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json"
                )

            # Cleanup temporary files
            try:
                if os.path.exists(upload_base_dir):
                    shutil.rmtree(upload_base_dir)
            except Exception as cleanup_error:
                st.warning(f"Could not clean up temporary files: {cleanup_error}")

        except Exception as e:
            st.error(f"An unexpected error occurred: {str(e)}")

# Instructions
st.sidebar.markdown("---")
st.sidebar.subheader("Instructions")
st.sidebar.markdown("""
1. Upload bidder documents (PDF format)
2. Optionally upload criteria file (JSON/Excel/PDF)
3. Click 'Run Evaluation' to process
4. View results and download reports
""")
