import streamlit as st
import json
import pandas as pd
import os
from datetime import datetime
from rfpScoring_system import RobustScoringSystem  # ✅ Updated import

# Streamlit UI Setup
st.set_page_config(page_title="RFP Evaluation Suite", layout="wide")
st.title("🛡️ RFP Evaluation Suite")
st.markdown("Automated scoring of bidder documents based on evaluation criteria using rule-based and LLM fallback systems.")

# Sidebar file uploads
with st.sidebar:
    st.header("📂 Upload Files")
    bidder_docs = st.file_uploader("Upload Bidder PDF Documents", type=["pdf"], accept_multiple_files=True)
    criteria_file = st.file_uploader("Upload Evaluation Criteria File (.json or .xlsx)", type=["json", "xlsx"])
    start_eval = st.button("🚀 Run Evaluation")

if start_eval:
    with st.spinner("Running evaluation..."):
        try:
            # Prepare upload directories
            bidder_dir = "uploaded_docs/bidder/"
            criteria_dir = "uploaded_docs/criteria/"
            os.makedirs(bidder_dir, exist_ok=True)
            os.makedirs(criteria_dir, exist_ok=True)

            # Save bidder documents
            for file in bidder_docs:
                with open(os.path.join(bidder_dir, file.name), "wb") as f:
                    f.write(file.read())

            # Save criteria file
            criteria_path = ""
            if criteria_file is not None:
                criteria_path = os.path.join(criteria_dir, criteria_file.name)
                with open(criteria_path, "wb") as f:
                    f.write(criteria_file.read())

            # Run evaluation
            scoring_system = RobustScoringSystem()
            results = scoring_system.run_robust_evaluation()

            if "error" in results:
                st.error(f"❌ Evaluation Failed: {results['error']}")
            else:
                st.success("✅ Evaluation Completed!")
                st.subheader("📊 Summary")
                st.write(f"**Total Bidders Evaluated:** {results['total_bidders']}")
                st.write(f"**Evaluation Timestamp:** {results['evaluation_timestamp']}")

                # Summary Table
                data = []
                for bidder_name, bidder_data in results["results"].items():
                    data.append({
                        "Bidder Name": bidder_name,
                        "Total Score": bidder_data["total_score"],
                        "Max Score": bidder_data["max_possible_score"],
                        "Percentage": bidder_data["percentage_score"],
                        "Status": bidder_data["qualification_status"]
                    })
                df_summary = pd.DataFrame(data)
                st.dataframe(df_summary, use_container_width=True)

                # Detailed View per Bidder
                st.subheader("🔍 Detailed Scores")
                for bidder_name, bidder_data in results["results"].items():
                    with st.expander(f"🔽 {bidder_name} - {bidder_data['percentage_score']:.1f}%"):
                        df_detailed = pd.DataFrame(bidder_data["individual_scores"])
                        st.dataframe(df_detailed)

                # Download Button
                result_json = json.dumps(results, indent=2)
                st.download_button(
                    label="📥 Download JSON Results",
                    data=result_json,
                    file_name=f"rfp_scoring_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json"
                )

        except Exception as e:
            st.error(f"⚠️ An unexpected error occurred: {str(e)}")
