from langchain_huggingface import Hugging<PERSON>aceEmbeddings
from langchain_community.document_loaders import TextLoader
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import PromptTemplate
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import FAISS
from langchain.chains import RetrievalQ<PERSON>
from langchain_ollama import ChatOllama

import os

llm = "llama3.2:3b"
model = ChatOllama(model = llm)

loader = TextLoader("combined_table.txt", encoding = "utf-8")
docs = loader.load()

splitter = RecursiveCharacterTextSplitter(chunk_size = 600, chunk_overlap = 100)
chunks = splitter.create_documents([docs[0].page_content])

embedding = HuggingFaceEmbeddings(model_name = "sentence-transformers/all-MiniLM-L6-v2")
vector_store = FAISS.from_documents(chunks, embedding)

retriever = vector_store.as_retriever(search_type = "similarity", search_kwargs = {"k" : 3})

prompt_template = PromptTemplate.from_template(
    """
    You are an assistant that answers based on technical evaluation criteria.

    Use the following context to answer the question:
    {context}

    Question: {question}
    """
)

rag_chain = (
    prompt_template | model | StrOutputParser()
)

query = "If the bidder is a firm registered in India through competent agency/authority for  10 years how much marks will be provided ?"
retrieved_docs = retriever.invoke(query)

# Format context from retrieved docs
context = "\n\n".join([doc.page_content for doc in retrieved_docs])

# Run the RAG chain
answer = rag_chain.invoke({
    "context": context,
    "question": query
})

print("Answer:", answer)