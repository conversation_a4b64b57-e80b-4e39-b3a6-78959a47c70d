Stack trace:
Frame         Function      Args
0007FFFF9660  00021006118E (00021028DEE8, 000210272B3E, 0007FFFF9660, 0007FFFF8560) msys-2.0.dll+0x2118E
0007FFFF9660  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF9660  0002100469F2 (00021028DF99, 0007FFFF9518, 0007FFFF9660, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9660  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9660  00021006A545 (0007FFFF9670, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9670, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC2D840000 ntdll.dll
7FFC2B970000 KERNEL32.DLL
7FFC2AE70000 KERNELBASE.dll
7FFC2BF00000 USER32.dll
000210040000 msys-2.0.dll
7FFC2B260000 win32u.dll
7FFC2C500000 GDI32.dll
7FFC2B4A0000 gdi32full.dll
7FFC2B3F0000 msvcp_win.dll
7FFC2ABA0000 ucrtbase.dll
7FFC2D740000 advapi32.dll
7FFC2BD80000 msvcrt.dll
7FFC2C920000 sechost.dll
7FFC2C6B0000 RPCRT4.dll
7FFC29EA0000 CRYPTBASE.DLL
7FFC2B290000 bcryptPrimitives.dll
7FFC2C0E0000 IMM32.DLL
