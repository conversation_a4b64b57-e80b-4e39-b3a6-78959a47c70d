#!/usr/bin/env python3
"""
Test script for the simplified app to verify AIMessage fix
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add current directory to path
sys.path.append('.')

def test_simple_scoring_system():
    """Test the simplified scoring system"""
    print("🧪 Testing Simplified Upload-Based Scoring System")
    print("="*60)
    
    try:
        # Import the new system
        from app import SimpleUploadScoringSystem
        
        # Create test directories
        test_dir = "test_simple_files"
        bidder_dir = os.path.join(test_dir, "bidder")
        os.makedirs(bidder_dir, exist_ok=True)
        
        # Copy existing bidder documents to test directory
        existing_docs = Path("documents")
        if existing_docs.exists():
            for doc_file in existing_docs.glob("*.pdf"):
                if "bidder" in doc_file.name.lower() or "dummy" in doc_file.name.lower():
                    shutil.copy2(doc_file, bidder_dir)
                    print(f"✅ Copied test file: {doc_file.name}")
        
        # Test the system
        print(f"\n🚀 Testing with files in: {bidder_dir}")
        
        scoring_system = SimpleUploadScoringSystem()
        results = scoring_system.process_uploaded_files(bidder_dir, "sample_criteria.json")
        
        if "error" in results:
            print(f"❌ Test failed: {results['error']}")
            return False
        
        print("✅ Test passed!")
        print(f"📊 Results: {results['total_bidders']} bidders evaluated")
        
        for bidder_name, data in results["results"].items():
            print(f"  - {bidder_name}: {data['percentage_score']:.1f}% ({data['qualification_status']})")
            
            # Check for AIMessage error
            for score in data["individual_scores"]:
                if "AIMessage" in score["rationale"]:
                    print(f"❌ AIMessage error found in rationale: {score['rationale']}")
                    return False
        
        print("✅ No AIMessage errors found!")
        
        # Cleanup
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return False

def test_llm_response_handling():
    """Test LLM response handling specifically"""
    print("\n🧪 Testing LLM Response Handling")
    print("="*40)
    
    try:
        from app import SimpleUploadScoringSystem
        from criteria_extractor import EvaluationCriteria
        
        system = SimpleUploadScoringSystem()
        
        # Create a test criteria
        test_criteria = EvaluationCriteria(
            serial_number="1",
            criteria_text="Test criteria for company registration",
            relevant_document="Registration certificate",
            marking_system="10-15 yrs = 10, >15 yrs = 20",
            max_marks="20"
        )
        
        # Test with sample bidder info
        test_bidder_info = "Company registered 12 years ago with good standing."
        
        # This should use rule-based scoring and not trigger LLM
        result = system.score_bidder_against_criteria(test_bidder_info, test_criteria)
        
        print(f"✅ Scoring result: {result['awarded_marks']}/{result['max_marks']}")
        print(f"✅ Method: {result['scoring_method']}")
        print(f"✅ Rationale: {result['rationale'][:50]}...")
        
        # Check for AIMessage in rationale
        if "AIMessage" in result['rationale']:
            print(f"❌ AIMessage error found: {result['rationale']}")
            return False
        
        print("✅ LLM response handling works correctly!")
        return True
        
    except Exception as e:
        print(f"❌ LLM response test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🎯 SIMPLIFIED APP TESTING")
    print("="*60)
    
    tests = [
        test_llm_response_handling,
        test_simple_scoring_system
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
    
    print(f"\n📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! The simplified app is working correctly.")
        print("\n🚀 The simplified app is running at: http://localhost:8501")
        print("\n🔧 Key fixes implemented:")
        print("   • Fixed AIMessage error by handling response.content")
        print("   • Simplified GUI with less clutter")
        print("   • Maintained all core functionality")
        print("   • PDF criteria support still available")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
